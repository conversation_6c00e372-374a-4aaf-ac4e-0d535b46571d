import { Controller, Get, Post, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { NotificationType } from './entities/notification.entity';

@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  async getUserNotifications(
    @Query('userId') userId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('isRead') isRead?: boolean,
    @Query('type') type?: NotificationType,
  ) {
    return this.notificationsService.findAll(userId, { page: Number(page), limit: Number(limit), isRead, type });
  }

  @Post('read/:id')
  async markAsRead(
    @Param('id') id: string,
    @Body('userId') userId: string,
  ) {
    return this.notificationsService.markAsRead(id, userId);
  }

  @Post('read-all')
  async markAllAsRead(@Body('userId') userId: string) {
    return this.notificationsService.markAllAsRead(userId);
  }

  @Delete(':id')
  async deleteNotification(
    @Param('id') id: string,
    @Body('userId') userId: string,
  ) {
    return this.notificationsService.delete(id, userId);
  }

  // Endpoint to create a notification for a user
  @Post('create')
  async createNotification(
    @Body() body: {
      userId: string;
      type: NotificationType;
      data: any;
    },
  ) {
    return this.notificationsService.create(body);
  }

  // Endpoint to trigger job created notifications
  @Post('notify-job-created')
  async notifyJobCreated(
    @Body() body: { jobId: string; employerId: string },
  ) {
    return this.notificationsService.notifyJobCreated(body.jobId, body.employerId);
  }

  // Endpoint to trigger job updated notifications
  @Post('notify-job-updated')
  async notifyJobUpdated(
    @Body() body: { jobId: string; employerId: string },
  ) {
    return this.notificationsService.notifyJobUpdated(body.jobId, body.employerId);
  }

  // Endpoint to trigger application status notifications
  @Post('notify-application-status')
  async notifyApplicationStatus(
    @Body() body: { applicationId: string; status: string },
  ) {
    return this.notificationsService.notifyApplicationStatus(body.applicationId, body.status);
  }
} 