<script setup>
import { ref, reactive, computed } from 'vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dialog from 'primevue/dialog'
import Dropdown from 'primevue/dropdown'
import Message from 'primevue/message'
import ConfirmDialog from 'primevue/confirmdialog'
import { useConfirm } from 'primevue/useconfirm'

const confirm = useConfirm()

// Sample data
const industries = ref([
  {
    id: 1,
    name: 'Technology',
    details: 'Software development, IT services, and digital solutions',
    categories: [
      { id: 1, name: 'Software Development', details: 'Custom software solutions and applications' },
      { id: 2, name: 'IT Consulting', details: 'Technology consulting and advisory services' },
      { id: 3, name: 'Cloud Services', details: 'Cloud infrastructure and platform services' }
    ],
    createdAt: new Date('2024-01-15')
  },
  {
    id: 2,
    name: 'Healthcare',
    details: 'Medical services, pharmaceuticals, and health technology',
    categories: [
      { id: 4, name: 'Medical Devices', details: 'Healthcare equipment and medical instruments' },
      { id: 5, name: 'Pharmaceuticals', details: 'Drug development and pharmaceutical products' }
    ],
    createdAt: new Date('2024-01-20')
  },
  {
    id: 3,
    name: 'Finance',
    details: 'Banking, investment, and financial services',
    categories: [
      { id: 6, name: 'Investment Banking', details: 'Corporate finance and investment services' },
      { id: 7, name: 'Insurance', details: 'Risk management and insurance products' }
    ],
    createdAt: new Date('2024-02-01')
  }
])

// Dialog states
const showIndustryDialog = ref(false)
const showCategoryDialog = ref(false)
const isEditMode = ref(false)
const selectedIndustry = ref(null)

// Form data
const industryForm = reactive({
  name: '',
  details: ''
})

const categoryForm = reactive({
  name: '',
  details: '',
  industryId: null
})

// Search and filters
const searchTerm = ref('')
const selectedIndustryFilter = ref(null)

// Computed properties
const filteredIndustries = computed(() => {
  let filtered = industries.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(industry => 
      industry.name.toLowerCase().includes(search) ||
      industry.details.toLowerCase().includes(search) ||
      industry.categories.some(cat => 
        cat.name.toLowerCase().includes(search) ||
        cat.details.toLowerCase().includes(search)
      )
    )
  }

  return filtered
})

const industryOptions = computed(() => 
  industries.value.map(industry => ({
    label: industry.name,
    value: industry.id
  }))
)

const allCategories = computed(() => {
  const categories = []
  industries.value.forEach(industry => {
    industry.categories.forEach(category => {
      categories.push({
        ...category,
        industryName: industry.name,
        industryId: industry.id
      })
    })
  })
  
  if (selectedIndustryFilter.value) {
    return categories.filter(cat => cat.industryId === selectedIndustryFilter.value)
  }
  
  return categories
})

const industryStats = computed(() => {
  const totalIndustries = industries.value.length
  const totalCategories = industries.value.reduce((sum, industry) => sum + industry.categories.length, 0)
  const avgCategoriesPerIndustry = totalIndustries > 0 ? Math.round(totalCategories / totalIndustries) : 0
  
  return {
    totalIndustries,
    totalCategories,
    avgCategoriesPerIndustry
  }
})

// Industry management
const openIndustryDialog = (industry = null) => {
  isEditMode.value = !!industry
  if (industry) {
    industryForm.name = industry.name
    industryForm.details = industry.details
    selectedIndustry.value = industry
  } else {
    resetIndustryForm()
  }
  showIndustryDialog.value = true
}

const resetIndustryForm = () => {
  industryForm.name = ''
  industryForm.details = ''
  selectedIndustry.value = null
}

const saveIndustry = () => {
  if (!industryForm.name.trim()) return

  if (isEditMode.value && selectedIndustry.value) {
    // Update existing industry
    const index = industries.value.findIndex(i => i.id === selectedIndustry.value.id)
    if (index !== -1) {
      industries.value[index] = {
        ...industries.value[index],
        name: industryForm.name.trim(),
        details: industryForm.details.trim()
      }
    }
  } else {
    // Add new industry
    const newIndustry = {
      id: Date.now(),
      name: industryForm.name.trim(),
      details: industryForm.details.trim(),
      categories: [],
      createdAt: new Date()
    }
    industries.value.push(newIndustry)
  }

  showIndustryDialog.value = false
  resetIndustryForm()
}

const deleteIndustry = (industry) => {
  confirm.require({
    message: `Are you sure you want to delete "${industry.name}" and all its categories?`,
    header: 'Delete Industry',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const index = industries.value.findIndex(i => i.id === industry.id)
      if (index !== -1) {
        industries.value.splice(index, 1)
      }
    }
  })
}

// Category management
const openCategoryDialog = (category = null, industryId = null) => {
  if (category) {
    categoryForm.name = category.name
    categoryForm.details = category.details
    categoryForm.industryId = category.industryId
    selectedIndustry.value = category
  } else {
    categoryForm.name = ''
    categoryForm.details = ''
    categoryForm.industryId = industryId
    selectedIndustry.value = null
  }
  showCategoryDialog.value = true
}

const saveCategory = () => {
  if (!categoryForm.name.trim() || !categoryForm.industryId) return

  const industryIndex = industries.value.findIndex(i => i.id === categoryForm.industryId)
  if (industryIndex === -1) return

  if (selectedIndustry.value && selectedIndustry.value.id) {
    // Update existing category
    const categoryIndex = industries.value[industryIndex].categories.findIndex(
      c => c.id === selectedIndustry.value.id
    )
    if (categoryIndex !== -1) {
      industries.value[industryIndex].categories[categoryIndex] = {
        ...industries.value[industryIndex].categories[categoryIndex],
        name: categoryForm.name.trim(),
        details: categoryForm.details.trim()
      }
    }
  } else {
    // Add new category
    const newCategory = {
      id: Date.now(),
      name: categoryForm.name.trim(),
      details: categoryForm.details.trim()
    }
    industries.value[industryIndex].categories.push(newCategory)
  }

  showCategoryDialog.value = false
  resetCategoryForm()
}

const resetCategoryForm = () => {
  categoryForm.name = ''
  categoryForm.details = ''
  categoryForm.industryId = null
  selectedIndustry.value = null
}

const deleteCategory = (category) => {
  confirm.require({
    message: `Are you sure you want to delete the category "${category.name}"?`,
    header: 'Delete Category',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const industryIndex = industries.value.findIndex(i => i.id === category.industryId)
      if (industryIndex !== -1) {
        const categoryIndex = industries.value[industryIndex].categories.findIndex(
          c => c.id === category.id
        )
        if (categoryIndex !== -1) {
          industries.value[industryIndex].categories.splice(categoryIndex, 1)
        }
      }
    }
  })
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

const clearFilters = () => {
  searchTerm.value = ''
  selectedIndustryFilter.value = null
}
</script>

<template>
  <div class="industries-view">
    <!-- Left Sidebar with Filters -->
    <div class="filters-sidebar">
      <div class="filters-header">
        <h3>
          <i class="pi pi-filter"></i>
          Filters
        </h3>
        <Button 
          icon="pi pi-filter-slash" 
          text
          rounded
          size="small"
          @click="clearFilters"
          :disabled="!searchTerm && !selectedIndustryFilter"
          v-tooltip="'Clear All Filters'"
        />
      </div>

      <div class="filters-content">
        <!-- Search -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-search"></i>
            Search
          </label>
          <InputText 
            v-model="searchTerm" 
            placeholder="Search industries..." 
            class="w-full"
          />
        </div>

        <!-- Industry Filter for Categories -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-building"></i>
            Filter Categories by Industry
          </label>
          <Dropdown
            v-model="selectedIndustryFilter"
            :options="industryOptions"
            option-label="label"
            option-value="value"
            placeholder="Select industry"
            :showClear="true"
            class="w-full"
          />
        </div>

        <!-- Statistics -->
        <div class="filter-stats">
          <h4>Statistics</h4>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">Total Industries</span>
              <span class="stat-value">{{ industryStats.totalIndustries }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Categories</span>
              <span class="stat-value">{{ industryStats.totalCategories }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Avg Categories</span>
              <span class="stat-value">{{ industryStats.avgCategoriesPerIndustry }}</span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h4>Quick Actions</h4>
          <div class="action-buttons">
            <Button 
              label="Add Industry" 
              icon="pi pi-plus" 
              class="w-full"
              @click="openIndustryDialog()"
            />
            <Button 
              label="Add Category" 
              icon="pi pi-tag" 
              severity="secondary"
              class="w-full"
              @click="openCategoryDialog()"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div>
            <h1>Industries</h1>
            <p>Manage industries and their categories</p>
          </div>
        </div>
      </div>

      <!-- Content Tabs -->
      <div class="content-tabs">
        <div class="tab-container">
          <!-- Industries Section -->
          <div class="tab-content">
            <div class="section-header">
              <h3>Industries</h3>
              <Button 
                label="Add Industry" 
                icon="pi pi-plus" 
                size="small"
                @click="openIndustryDialog()"
              />
            </div>
            
            <div class="table-container">
              <DataTable 
                :value="filteredIndustries" 
                class="industries-table"
                :paginator="true" 
                :rows="10"
                stripedRows
                :rowHover="true"
                :scrollable="true"
                scroll-height="300px"
              >
                <Column field="name" header="Industry Name" sortable>
                  <template #body="slotProps">
                    <div class="industry-name">
                      <i class="pi pi-building industry-icon"></i>
                      <span>{{ slotProps.data.name }}</span>
                    </div>
                  </template>
                </Column>
                
                <Column field="details" header="Details" sortable>
                  <template #body="slotProps">
                    <span class="industry-details">{{ slotProps.data.details }}</span>
                  </template>
                </Column>
                
                <Column header="Categories" sortable>
                  <template #body="slotProps">
                    <div class="categories-count">
                      <span class="count-badge">{{ slotProps.data.categories.length }}</span>
                      <span>categories</span>
                    </div>
                  </template>
                </Column>
                
                <Column field="createdAt" header="Created" sortable>
                  <template #body="slotProps">
                    <span class="created-date">{{ formatDate(slotProps.data.createdAt) }}</span>
                  </template>
                </Column>
                
                <Column header="Actions">
                  <template #body="slotProps">
                    <div class="action-buttons">
                      <Button 
                        icon="pi pi-plus" 
                        text 
                        rounded 
                        size="small" 
                        @click="openCategoryDialog(null, slotProps.data.id)"
                        v-tooltip="'Add Category'"
                      />
                      <Button 
                        icon="pi pi-pencil" 
                        text 
                        rounded 
                        size="small" 
                        @click="openIndustryDialog(slotProps.data)"
                        v-tooltip="'Edit Industry'"
                      />
                      <Button 
                        icon="pi pi-trash" 
                        text 
                        rounded 
                        size="small" 
                        severity="danger" 
                        @click="deleteIndustry(slotProps.data)"
                        v-tooltip="'Delete Industry'"
                      />
                    </div>
                  </template>
                </Column>
              </DataTable>
            </div>
          </div>

          <!-- Categories Section -->
          <div class="tab-content">
            <div class="section-header">
              <h3>All Categories</h3>
              <Button 
                label="Add Category" 
                icon="pi pi-plus" 
                size="small"
                @click="openCategoryDialog()"
              />
            </div>
            
            <div class="table-container">
              <DataTable 
                :value="allCategories" 
                class="categories-table"
                :paginator="true" 
                :rows="10"
                stripedRows
                :rowHover="true"
                :scrollable="true"
                scroll-height="300px"
              >
                <Column field="name" header="Category Name" sortable>
                  <template #body="slotProps">
                    <div class="category-name">
                      <i class="pi pi-tag category-icon"></i>
                      <span>{{ slotProps.data.name }}</span>
                    </div>
                  </template>
                </Column>
                
                <Column field="details" header="Details" sortable>
                  <template #body="slotProps">
                    <span class="category-details">{{ slotProps.data.details }}</span>
                  </template>
                </Column>
                
                <Column field="industryName" header="Industry" sortable>
                  <template #body="slotProps">
                    <span class="industry-badge">{{ slotProps.data.industryName }}</span>
                  </template>
                </Column>
                
                <Column header="Actions">
                  <template #body="slotProps">
                    <div class="action-buttons">
                      <Button 
                        icon="pi pi-pencil" 
                        text 
                        rounded 
                        size="small" 
                        @click="openCategoryDialog(slotProps.data)"
                        v-tooltip="'Edit Category'"
                      />
                      <Button 
                        icon="pi pi-trash" 
                        text 
                        rounded 
                        size="small" 
                        severity="danger" 
                        @click="deleteCategory(slotProps.data)"
                        v-tooltip="'Delete Category'"
                      />
                    </div>
                  </template>
                </Column>
              </DataTable>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Industry Dialog -->
    <Dialog 
      v-model:visible="showIndustryDialog" 
      :header="isEditMode ? 'Edit Industry' : 'Add Industry'"
      :modal="true" 
      class="industry-dialog"
      :style="{ width: '500px' }"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="industry-name">Industry Name *</label>
          <InputText 
            id="industry-name"
            v-model="industryForm.name" 
            placeholder="Enter industry name"
            class="w-full"
            :class="{ 'p-invalid': !industryForm.name.trim() }"
          />
        </div>
        
        <div class="form-group">
          <label for="industry-details">Details</label>
          <Textarea 
            id="industry-details"
            v-model="industryForm.details" 
            placeholder="Enter industry details and description"
            rows="4"
            class="w-full"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showIndustryDialog = false"
          />
          <Button 
            :label="isEditMode ? 'Update' : 'Create'" 
            @click="saveIndustry"
            :disabled="!industryForm.name.trim()"
          />
        </div>
      </template>
    </Dialog>

    <!-- Category Dialog -->
    <Dialog 
      v-model:visible="showCategoryDialog" 
      :header="selectedIndustry?.id ? 'Edit Category' : 'Add Category'"
      :modal="true" 
      class="category-dialog"
      :style="{ width: '500px' }"
    >
      <div class="dialog-content">
        <div class="form-group">
          <label for="category-industry">Industry *</label>
          <Dropdown
            id="category-industry"
            v-model="categoryForm.industryId"
            :options="industryOptions"
            option-label="label"
            option-value="value"
            placeholder="Select an industry"
            class="w-full"
            :class="{ 'p-invalid': !categoryForm.industryId }"
          />
        </div>
        
        <div class="form-group">
          <label for="category-name">Category Name *</label>
          <InputText 
            id="category-name"
            v-model="categoryForm.name" 
            placeholder="Enter category name"
            class="w-full"
            :class="{ 'p-invalid': !categoryForm.name.trim() }"
          />
        </div>
        
        <div class="form-group">
          <label for="category-details">Details</label>
          <Textarea 
            id="category-details"
            v-model="categoryForm.details" 
            placeholder="Enter category details and description"
            rows="4"
            class="w-full"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showCategoryDialog = false"
          />
          <Button 
            :label="selectedIndustry?.id ? 'Update' : 'Create'" 
            @click="saveCategory"
            :disabled="!categoryForm.name.trim() || !categoryForm.industryId"
          />
        </div>
      </template>
    </Dialog>

    <ConfirmDialog />
  </div>
</template>

<style scoped>
.industries-view {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Filters Sidebar */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.quick-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.quick-actions h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-content p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.content-tabs {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.industries-table,
.categories-table {
  height: 100%;
}

.industry-name,
.category-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.industry-icon {
  color: var(--p-primary-color);
  font-size: 1rem;
}

.category-icon {
  color: var(--p-orange-500);
  font-size: 0.875rem;
}

.industry-details,
.category-details {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.categories-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.count-badge {
  background: var(--p-primary-100);
  color: var(--p-primary-700);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.industry-badge {
  background: var(--p-surface-100);
  color: var(--p-text-color);
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
}

:global(.dark) .industry-badge {
  background: var(--p-surface-700);
}

.created-date {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .industries-view {
    flex-direction: column;
    height: auto;
  }
  
  .filters-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
  }
  
  .main-content {
    order: 1;
    height: 60vh;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .content-tabs {
    padding: 1rem;
  }
  
  .industry-details,
  .category-details {
    max-width: 200px;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>