<template>
  <AppLayout>
    <div class="saved-jobs-page">
      <div class="saved-jobs-content">
        <!-- <PERSON> Header -->
        <div class="page-header">
          <div class="header-content">
            <div class="header-left">
              <i class="pi pi-heart header-icon"></i>
              <div class="header-text">
                <h1>{{ t('dashboard.savedJobs') }}</h1>
                <p>Manage your saved job opportunities</p>
              </div>
            </div>
            <div class="header-actions">
              <Button 
                @click="refreshSavedJobs"
                :loading="isLoading"
                icon="pi pi-refresh"
                severity="secondary"
                outlined
                v-tooltip="'Refresh Saved Jobs'"
              />
              <Button 
                @click="clearAllSaved"
                icon="pi pi-trash"
                severity="danger"
                outlined
                :disabled="savedJobs.length === 0"
                v-tooltip="'Clear All Saved Jobs'"
              />
            </div>
          </div>
        </div>

        <!-- Saved Jobs Stats -->
        <div class="stats-overview">
          <div class="stat-card total">
            <div class="stat-icon">
              <i class="pi pi-heart"></i>
            </div>
            <div class="stat-content">
              <h3>{{ savedJobs.length }}</h3>
              <p>Total Saved Jobs</p>
            </div>
          </div>
          <div class="stat-card recent">
            <div class="stat-icon">
              <i class="pi pi-clock"></i>
            </div>
            <div class="stat-content">
              <h3>{{ recentlySavedJobs.length }}</h3>
              <p>Saved This Week</p>
            </div>
          </div>
          <div class="stat-card urgent">
            <div class="stat-icon">
              <i class="pi pi-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
              <h3>{{ urgentSavedJobs.length }}</h3>
              <p>Urgent Jobs</p>
            </div>
          </div>
          <div class="stat-card applied">
            <div class="stat-icon">
              <i class="pi pi-send"></i>
            </div>
            <div class="stat-content">
              <h3>{{ appliedFromSaved.length }}</h3>
              <p>Applied From Saved</p>
            </div>
          </div>
        </div>

        <!-- Filter and Sort Controls -->
        <div class="controls-section">
          <div class="search-controls">
            <div class="search-input-wrapper">
              <i class="pi pi-search search-icon"></i>
              <InputText
                v-model="searchQuery"
                placeholder="Search saved jobs..."
                class="search-input"
              />
            </div>
          </div>
          
          <div class="filter-controls">
            <Dropdown
              v-model="selectedIndustry"
              :options="industryOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="All Industries"
              class="filter-dropdown"
              showClear
            />
            
            <Dropdown
              v-model="selectedJobType"
              :options="jobTypeOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="All Job Types"
              class="filter-dropdown"
              showClear
            />
            
            <Dropdown
              v-model="sortBy"
              :options="sortOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="Sort by"
              class="sort-dropdown"
            />
          </div>
        </div>

        <!-- Saved Jobs List -->
        <div class="saved-jobs-list" v-if="filteredSavedJobs.length > 0">
          <div 
            v-for="job in filteredSavedJobs" 
            :key="job.id"
            :class="['saved-job-card', { 'urgent': job.isUrgent, 'applied': hasApplied(job.id) }]"
          >
            <div class="job-header">
              <div class="job-avatar">
                <Avatar 
                  :label="job.company.charAt(0).toUpperCase()" 
                  :style="{ backgroundColor: job.color }"
                  shape="circle"
                  size="large"
                />
              </div>
              <div class="job-basic-info">
                <h3 class="job-title" @click="viewJobDetails(job)">{{ job.title }}</h3>
                <p class="job-company">{{ job.company }}</p>
                <div class="job-badges">
                  <Tag 
                    v-if="job.isFeatured"
                    :value="t('jobs.featured')" 
                    severity="success"
                    class="featured-badge"
                  />
                  <Tag 
                    v-if="job.isUrgent"
                    :value="t('jobs.urgent')" 
                    severity="danger"
                    class="urgent-badge"
                  />
                  <Tag 
                    :value="job.jobTypeName" 
                    severity="info"
                    class="job-type-badge"
                  />
                  <Tag 
                    v-if="hasApplied(job.id)"
                    value="Applied" 
                    severity="success"
                    class="applied-badge"
                  />
                </div>
              </div>
              <div class="job-actions">
                <Button 
                  icon="pi pi-heart-fill" 
                  text 
                  size="small"
                  @click="removeSavedJob(job)"
                  v-tooltip="'Remove from Saved'"
                  class="remove-saved-btn"
                />
                <Button 
                  icon="pi pi-external-link" 
                  text 
                  size="small"
                  @click="viewJobDetails(job)"
                  v-tooltip="'View Details'"
                />
              </div>
            </div>

            <div class="job-content">
              <p class="job-description">{{ job.description }}</p>
              
              <div class="job-details">
                <div class="job-meta">
                  <span class="job-location">
                    <i class="pi pi-map-marker"></i>
                    {{ job.location }}
                  </span>
                  <span class="job-salary">
                    <i class="pi pi-indian-rupee"></i>
                    ₹{{ job.salaryMin.toLocaleString() }} - ₹{{ job.salaryMax.toLocaleString() }}
                  </span>
                  <span class="job-posted">
                    <i class="pi pi-calendar"></i>
                    {{ t('jobs.postedAgo', { time: formatDate(job.postedDate) }) }}
                  </span>
                  <span class="job-saved">
                    <i class="pi pi-heart"></i>
                    Saved {{ formatDate(job.savedDate) }}
                  </span>
                </div>

                <div class="job-skills" v-if="job.skills && job.skills.length > 0">
                  <Tag 
                    v-for="skill in job.skills.slice(0, 4)" 
                    :key="skill"
                    :value="skill"
                    class="skill-tag"
                  />
                  <span v-if="job.skills.length > 4" class="more-skills">
                    +{{ job.skills.length - 4 }} more
                  </span>
                </div>
              </div>
            </div>

            <div class="job-footer">
              <div class="job-stats">
                <span class="applicants-count">
                  <i class="pi pi-users"></i>
                  {{ job.applicationsCount }} applicants
                </span>
                <span class="views-count">
                  <i class="pi pi-eye"></i>
                  {{ job.viewsCount }} views
                </span>
                <span class="closing-date" v-if="job.closingDate">
                  <i class="pi pi-clock"></i>
                  Closes {{ formatDate(job.closingDate) }}
                </span>
              </div>
              <div class="job-footer-actions">
                <Button 
                  @click="shareJob(job)"
                  icon="pi pi-share-alt"
                  text
                  size="small"
                  v-tooltip="'Share Job'"
                />
                <Button 
                  @click="applyToJob(job)"
                  :label="hasApplied(job.id) ? t('jobs.applied') : t('dashboard.applyNow')"
                  class="apply-button"
                  :disabled="hasApplied(job.id)"
                >
                  <template v-if="hasApplied(job.id)">
                    <i class="pi pi-check"></i>
                    {{ t('jobs.applied') }}
                  </template>
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading" class="empty-state">
          <div class="empty-content">
            <i class="pi pi-heart empty-icon"></i>
            <h3>{{ getEmptyStateTitle() }}</h3>
            <p>{{ getEmptyStateMessage() }}</p>
            <div class="empty-actions">
              <Button 
                icon="pi pi-search"
                :label="searchQuery ? 'Clear Search' : 'Browse Jobs'"
                @click="searchQuery ? clearSearch() : searchJobs()"
              />
              <Button 
                v-if="searchQuery"
                @click="clearSearch"
                icon="pi pi-times"
                label="Clear Search"
                outlined
              />
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="loading-state">
          <i class="pi pi-spin pi-spinner loading-icon"></i>
          <p>Loading your saved jobs...</p>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'
import AppLayout from '@/components/AppLayout.vue'
import alertManager from '@/utils/alertManager'

const router = useRouter()
const { t } = useI18n()

// State
const isLoading = ref(false)
const searchQuery = ref('')
const selectedIndustry = ref(null)
const selectedJobType = ref(null)
const sortBy = ref('savedDate')

// Mock saved jobs data
const savedJobs = ref([
  {
    id: 1,
    title: 'Construction Worker',
    company: 'ABC Construction',
    location: 'Mumbai, Maharashtra',
    salaryMin: 25000,
    salaryMax: 35000,
    description: 'Experienced construction worker needed for residential building projects. Must have knowledge of safety protocols and basic construction techniques.',
    skills: ['Construction', 'Safety Protocols', 'Blueprint Reading', 'Heavy Machinery'],
    jobTypeName: 'Full-time',
    applicationsCount: 45,
    viewsCount: 234,
    postedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    savedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    closingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#3b82f6',
    isFeatured: true,
    isUrgent: false,
    industry: 'construction'
  },
  {
    id: 2,
    title: 'Warehouse Associate',
    company: 'XYZ Logistics',
    location: 'Delhi, NCR',
    salaryMin: 18000,
    salaryMax: 25000,
    description: 'Looking for warehouse associates to handle inventory management, packing, and shipping operations.',
    skills: ['Inventory Management', 'Forklift Operation', 'Package Handling'],
    jobTypeName: 'Full-time',
    applicationsCount: 67,
    viewsCount: 189,
    postedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    savedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
    closingDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#10b981',
    isFeatured: false,
    isUrgent: true,
    industry: 'logistics'
  },
  {
    id: 3,
    title: 'Security Guard',
    company: 'SecureTech Services',
    location: 'Bangalore, Karnataka',
    salaryMin: 15000,
    salaryMax: 22000,
    description: 'Security guard position for corporate office building. Night shift available with additional allowances.',
    skills: ['Security Protocols', 'Surveillance', 'Emergency Response'],
    jobTypeName: 'Full-time',
    applicationsCount: 23,
    viewsCount: 156,
    postedDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    savedDate: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
    closingDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#ef4444',
    isFeatured: false,
    isUrgent: false,
    industry: 'security'
  },
  {
    id: 4,
    title: 'Delivery Driver',
    company: 'FastDelivery Inc.',
    location: 'Chennai, Tamil Nadu',
    salaryMin: 20000,
    salaryMax: 30000,
    description: 'Delivery driver for e-commerce packages. Own vehicle preferred but company vehicle available.',
    skills: ['Driving License', 'Navigation', 'Customer Service'],
    jobTypeName: 'Full-time',
    applicationsCount: 89,
    viewsCount: 345,
    postedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    savedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    closingDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#8b5cf6',
    isFeatured: true,
    isUrgent: true,
    industry: 'logistics'
  },
  {
    id: 5,
    title: 'Maintenance Technician',
    company: 'Industrial Solutions',
    location: 'Pune, Maharashtra',
    salaryMin: 28000,
    salaryMax: 40000,
    description: 'Maintenance technician for industrial equipment. Experience with electrical and mechanical systems required.',
    skills: ['Electrical Systems', 'Mechanical Repair', 'Troubleshooting', 'Preventive Maintenance'],
    jobTypeName: 'Full-time',
    applicationsCount: 34,
    viewsCount: 178,
    postedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
    savedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    closingDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#f59e0b',
    isFeatured: false,
    isUrgent: false,
    industry: 'maintenance'
  }
])

// Mock applied jobs
const appliedJobIds = ref(new Set([2, 4]))

// Filter options
const industryOptions = [
  { label: 'Construction', value: 'construction' },
  { label: 'Logistics', value: 'logistics' },
  { label: 'Security', value: 'security' },
  { label: 'Maintenance', value: 'maintenance' },
  { label: 'Manufacturing', value: 'manufacturing' }
]

const jobTypeOptions = [
  { label: 'Full-time', value: 'Full-time' },
  { label: 'Part-time', value: 'Part-time' },
  { label: 'Contract', value: 'Contract' },
  { label: 'Temporary', value: 'Temporary' }
]

const sortOptions = [
  { label: 'Recently Saved', value: 'savedDate' },
  { label: 'Recently Posted', value: 'postedDate' },
  { label: 'Salary: High to Low', value: 'salaryMax' },
  { label: 'Salary: Low to High', value: 'salaryMin' },
  { label: 'Closing Soon', value: 'closingDate' },
  { label: 'Most Applicants', value: 'applicationsCount' }
]

// Computed properties
const recentlySavedJobs = computed(() => {
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  return savedJobs.value.filter(job => new Date(job.savedDate) >= oneWeekAgo)
})

const urgentSavedJobs = computed(() => 
  savedJobs.value.filter(job => job.isUrgent)
)

const appliedFromSaved = computed(() => 
  savedJobs.value.filter(job => appliedJobIds.value.has(job.id))
)

const filteredSavedJobs = computed(() => {
  let filtered = [...savedJobs.value]
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(job => 
      job.title.toLowerCase().includes(query) ||
      job.company.toLowerCase().includes(query) ||
      job.location.toLowerCase().includes(query) ||
      job.skills.some(skill => skill.toLowerCase().includes(query))
    )
  }
  
  // Apply industry filter
  if (selectedIndustry.value) {
    filtered = filtered.filter(job => job.industry === selectedIndustry.value)
  }
  
  // Apply job type filter
  if (selectedJobType.value) {
    filtered = filtered.filter(job => job.jobTypeName === selectedJobType.value)
  }
  
  // Apply sorting
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'savedDate':
        return new Date(b.savedDate) - new Date(a.savedDate)
      case 'postedDate':
        return new Date(b.postedDate) - new Date(a.postedDate)
      case 'salaryMax':
        return b.salaryMax - a.salaryMax
      case 'salaryMin':
        return a.salaryMin - b.salaryMin
      case 'closingDate':
        return new Date(a.closingDate) - new Date(b.closingDate)
      case 'applicationsCount':
        return b.applicationsCount - a.applicationsCount
      default:
        return 0
    }
  })
  
  return filtered
})

// Helper functions
const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '1 day ago'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  return `${Math.floor(diffDays / 30)} months ago`
}

const hasApplied = (jobId) => {
  return appliedJobIds.value.has(jobId)
}

const getEmptyStateTitle = () => {
  if (searchQuery.value) {
    return 'No matching saved jobs'
  }
  return 'No saved jobs yet'
}

const getEmptyStateMessage = () => {
  if (searchQuery.value) {
    return 'Try adjusting your search terms or filters to find saved jobs.'
  }
  return 'Start saving jobs you\'re interested in to keep track of them here.'
}

// Actions
const refreshSavedJobs = () => {
  isLoading.value = true
  // Simulate API call
  setTimeout(() => {
    isLoading.value = false
    alertManager.showSuccess('Refreshed', 'Saved jobs have been refreshed')
  }, 1000)
}

const clearAllSaved = async () => {
  const confirmed = await alertManager.showConfirm(
    'Clear All Saved Jobs',
    'Are you sure you want to remove all saved jobs? This action cannot be undone.',
    {
      confirmText: 'Clear All',
      cancelText: 'Cancel'
    }
  )
  
  if (confirmed) {
    savedJobs.value = []
    alertManager.showSuccess('Cleared', 'All saved jobs have been removed')
  }
}

const removeSavedJob = async (job) => {
  const confirmed = await alertManager.showConfirm(
    'Remove Saved Job',
    `Remove "${job.title}" from your saved jobs?`,
    {
      confirmText: 'Remove',
      cancelText: 'Cancel'
    }
  )
  
  if (confirmed) {
    const index = savedJobs.value.findIndex(j => j.id === job.id)
    if (index !== -1) {
      savedJobs.value.splice(index, 1)
      alertManager.showSuccess('Removed', 'Job removed from saved list')
    }
  }
}

const viewJobDetails = (job) => {
  router.push(`/employer/jobs/${job.id}`)
}

const applyToJob = (job) => {
  if (!hasApplied(job.id)) {
    appliedJobIds.value.add(job.id)
    alertManager.showSuccess('Application Submitted', `Your application for "${job.title}" has been submitted successfully!`)
  }
}

const shareJob = (job) => {
  const jobUrl = `${window.location.origin}/employer/jobs/${job.id}`
  
  if (navigator.share) {
    navigator.share({
      title: job.title,
      text: `Check out this job: ${job.title} at ${job.company}`,
      url: jobUrl
    })
  } else {
    navigator.clipboard.writeText(jobUrl).then(() => {
      alertManager.showSuccess('Link Copied', 'Job URL copied to clipboard')
    }).catch(() => {
      alertManager.showError('Copy Failed', 'Failed to copy link to clipboard')
    })
  }
}

const searchJobs = () => {
  router.push('/employer/jobs')
}

const clearSearch = () => {
  searchQuery.value = ''
  selectedIndustry.value = null
  selectedJobType.value = null
}

onMounted(() => {
  // Load saved jobs data
})
</script>

<style scoped>
.saved-jobs-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.saved-jobs-content {
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 2rem;
  color: var(--red-500);
}

.header-text h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.header-text p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-card.total .stat-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--red-500);
}

.stat-card.recent .stat-icon {
  background: rgba(59, 130, 246, 0.1);
  color: var(--blue-500);
}

.stat-card.urgent .stat-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--yellow-500);
}

.stat-card.applied .stat-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--green-500);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1.75rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.controls-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-controls {
  flex: 1;
  min-width: 300px;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
}

.search-input {
  width: 100% !important;
  padding-left: 3rem !important;
  height: 48px !important;
  border-radius: 8px !important;
}

.filter-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-dropdown,
.sort-dropdown {
  min-width: 150px;
}

.saved-jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.saved-job-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all var(--transition-duration) ease;
  position: relative;
}

.saved-job-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.saved-job-card.urgent {
  border-left: 4px solid var(--red-500);
}

.saved-job-card.applied {
  border-left: 4px solid var(--green-500);
}

.job-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.job-basic-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  cursor: pointer;
}

.job-title:hover {
  color: var(--primary-color);
}

.job-company {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.job-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.featured-badge,
.urgent-badge,
.job-type-badge,
.applied-badge {
  font-size: 0.75rem !important;
}

.job-actions {
  display: flex;
  gap: 0.25rem;
}

.remove-saved-btn {
  color: var(--red-500) !important;
}

.job-content {
  margin-bottom: 1rem;
}

.job-description {
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.job-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.job-location,
.job-salary,
.job-posted,
.job-saved {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
}

.job-location i,
.job-posted i,
.job-saved i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-salary {
  font-weight: 600;
  color: var(--primary-color);
}

.job-salary i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-saved i {
  color: var(--red-500);
}

.job-skills {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.skill-tag {
  font-size: 0.75rem !important;
  background: var(--surface-100) !important;
  color: var(--text-color-secondary) !important;
}

.more-skills {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  font-style: italic;
}

.job-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.job-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.applicants-count,
.views-count,
.closing-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.applicants-count i,
.views-count i,
.closing-date i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.job-footer-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.apply-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  font-weight: 600 !important;
}

.apply-button:disabled {
  background: var(--green-500) !important;
  border-color: var(--green-500) !important;
  opacity: 1 !important;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-content {
  max-width: 400px;
}

.empty-icon,
.loading-icon {
  font-size: 4rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.empty-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .saved-jobs-content {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions {
    align-self: flex-end;
  }

  .header-text h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .controls-section {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-controls {
    min-width: auto;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-dropdown,
  .sort-dropdown {
    flex: 1;
    min-width: auto;
  }

  .job-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .job-actions {
    align-self: flex-end;
  }

  .job-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .job-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .job-footer-actions {
    width: 100%;
    justify-content: space-between;
  }

  .apply-button {
    flex: 1;
  }

  .empty-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .filter-controls {
    flex-direction: column;
  }
}
</style>