import { createRouter, createWebHist<PERSON> } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import LandingPage from '@/views/LandingPage.vue'
import Login from '@/views/Login.vue'
import Dashboard from '@/views/Dashboard.vue'
import JobsList from '@/views/JobsList.vue'
import JobDetails from '@/views/JobDetails.vue'
import SavedJobs from '@/views/SavedJobs.vue'
import Applications from '@/views/Applications.vue'
import Profile from '@/views/Profile.vue'
import Notifications from '@/views/Notifications.vue'

const routes = [
  // Landing page - main entry point
  {
    path: '/',
    name: 'Landing',
    component: LandingPage,
    meta: { layout: 'landing', requiresGuest: true }
  },
  
  // Job seeker app routes
  {
    path: '/jobseeker',
    redirect: '/jobseeker/dashboard'
  },
  {
    path: '/jobseeker/login',
    name: 'JobSeekerLogin',
    component: Login,
    meta: { requiresGuest: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/dashboard',
    name: 'JobSeekerDashboard',
    component: Dashboard,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/jobs',
    name: 'JobSearch',
    component: JobsList,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/jobs/saved',
    name: 'SavedJobs',
    component: SavedJobs,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/jobs/:id',
    name: 'JobDetails',
    component: JobDetails,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/applications',
    name: 'MyApplications',
    component: Applications,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/profile',
    name: 'JobSeekerProfile',
    component: Profile,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  {
    path: '/jobseeker/notifications',
    name: 'Notifications',
    component: Notifications,
    meta: { requiresAuth: true, app: 'jobseeker' }
  },
  
  // Legacy redirects for backward compatibility
  {
    path: '/login',
    redirect: '/jobseeker/login'
  },
  {
    path: '/dashboard',
    redirect: '/jobseeker/dashboard'
  },
  {
    path: '/jobs',
    redirect: '/jobseeker/jobs'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (!authStore.isInitialised) {
    try{
    await authStore.initializeAuth()
    } catch(error) {
      console.log('Failed to get the profile')
    }
  }

  if (authStore.isAuthenticated) {
    if (!authStore.enums) {
      await authStore.getEnumData()
    }

    if (!authStore.industries) {
      await authStore.getIndustries()
    }
  }

  if (!authStore.isAuthenticated && to?.path !== '/jobseeker/login') {
    next('/jobseeker/login')
  } else {
    if (authStore.isAuthenticated && !authStore.isProfileComplete && to?.path !== '/jobseeker/profile') {
      next('/jobseeker/profile')
    } else {
      next()
    }
  }
})

export default router