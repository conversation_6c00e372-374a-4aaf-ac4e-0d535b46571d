import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsUUID,
  IsArray,
  IsBoolean,
  IsInt,
  Min,
  IsEmail,
  IsPhoneNumber,
} from 'class-validator';
import {
  JobType,
  PaymentType,
  JobUrgency,
  ExperienceLevel,
  ContactDisplayType,
} from '../entities/job.entity';
import { PartialType } from '@nestjs/mapped-types';
import { CreateJobDto } from './create-job.dto';

export class UpdateJobDto extends PartialType(CreateJobDto) {
  @ApiProperty({ description: 'Job title', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Job description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Industry ID', required: false })
  @IsUUID()
  @IsOptional()
  industryId?: string;

  @ApiProperty({ description: 'Salary amount', required: false })
  @IsNumber()
  @IsOptional()
  salary?: number;

  @ApiProperty({
    description: 'Job type (FULL_TIME, PART_TIME, CONTRACT, or DAILY_WAGE)',
    enum: JobType,
    required: false,
  })
  @IsEnum(JobType)
  @IsOptional()
  jobType?: JobType;

  @ApiProperty({
    description: 'Payment frequency (DAILY, WEEKLY, BI_WEEKLY, or MONTHLY)',
    enum: PaymentType,
    required: false,
  })
  @IsEnum(PaymentType)
  @IsOptional()
  paymentType?: PaymentType;

  @ApiProperty({ description: 'Job location coordinates', required: false })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({
    description: 'Job urgency level',
    enum: JobUrgency,
    required: false,
  })
  @IsEnum(JobUrgency)
  @IsOptional()
  urgency?: JobUrgency;

  @ApiProperty({
    description: 'Required experience level',
    enum: ExperienceLevel,
    required: false,
  })
  @IsEnum(ExperienceLevel)
  @IsOptional()
  experienceLevel?: ExperienceLevel;

  @ApiProperty({
    description: 'List of job benefits',
    type: [String],
    example: ['Health Insurance', 'Overtime Pay', 'Weekly Off'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  benefits?: string[];

  @ApiProperty({
    description: 'List of job requirements',
    type: [String],
    example: ['Valid Driver License', 'Safety Training Certificate'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requirements?: string[];

  @ApiProperty({
    description: 'List of job responsibilities',
    type: [String],
    example: ['Operating heavy machinery', 'Maintaining safety standards'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  responsibilities?: string[];

  @ApiProperty({
    description: 'Required skills for the job',
    type: [String],
    example: ['Heavy Equipment Operation', 'Safety Protocols'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];

  @ApiProperty({
    description: 'URL of the job thumbnail image',
    required: false,
  })
  @IsString()
  @IsOptional()
  thumbnail?: string;

  @ApiProperty({
    description: 'URLs of additional job images',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[];

  @ApiProperty({
    description: 'Whether to show contact information on job details',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  showContact?: boolean;

  @ApiProperty({
    description: 'Type of contact information to display',
    enum: ContactDisplayType,
    required: false,
  })
  @IsEnum(ContactDisplayType)
  @IsOptional()
  contactDisplayType?: ContactDisplayType;

  @ApiProperty({
    description: 'Contact phone number',
    example: '+1234567890',
    required: false,
  })
  @IsPhoneNumber()
  @IsOptional()
  contactPhone?: string;

  @ApiProperty({
    description: 'Contact email address',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  contactEmail?: string;

  @ApiProperty({
    description: 'Name of the contact person',
    example: 'John Doe',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPerson?: string;

  @ApiProperty({
    description: 'Number of vacancies available',
    minimum: 1,
    required: false,
  })
  @IsInt()
  @Min(1)
  @IsOptional()
  vacancies?: number;

  @ApiProperty({
    description: 'Working hours details',
    example: '8 hours per day, 6 days a week',
    required: false,
  })
  @IsString()
  @IsOptional()
  workingHours?: string;

  @ApiProperty({
    description: 'Accommodation details if provided',
    example: 'Shared accommodation provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  accommodation?: string;

  @ApiProperty({
    description: 'Transportation details if provided',
    example: 'Transportation allowance provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  transportation?: string;

  @ApiProperty({
    description: 'Food provision details',
    example: 'Lunch provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  foodProvided?: string;

  @ApiProperty({
    description: 'Safety equipment provided',
    example: 'Safety boots, helmet, and gloves provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  safetyEquipment?: string;

  @ApiProperty({
    description: 'Training details if provided',
    example: 'On-the-job training provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  trainingProvided?: string;
}
