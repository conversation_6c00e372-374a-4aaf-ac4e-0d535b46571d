import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const applicationsService = {
  // Get user's applications
  async getUserApplications(params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.APPLICATIONS.BY_USER, { params })
      return {
        success: true,
        data: response.applications || response.data || [],
        pagination: response.pagination,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch applications')
    }
  },

  // Get application by ID
  async getById(id) {
    try {
      const response = await httpClient.get(ENDPOINTS.APPLICATIONS.DETAILS(id))
      return {
        success: true,
        data: response.application || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Application not found')
    }
  },

  // Apply to a job
  async create(applicationData) {
    try {
      const response = await httpClient.post(ENDPOINTS.APPLICATIONS.CREATE, applicationData)
      return {
        success: true,
        data: response.application || response.data,
        message: response.message || 'Application submitted successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to submit application')
    }
  },

  // Update application
  async update(id, applicationData) {
    try {
      const response = await httpClient.put(ENDPOINTS.APPLICATIONS.UPDATE(id), applicationData)
      return {
        success: true,
        data: response.application || response.data,
        message: response.message || 'Application updated successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to update application')
    }
  },

  // Withdraw application
  async withdraw(id) {
    try {
      const response = await httpClient.post(ENDPOINTS.APPLICATIONS.WITHDRAW(id))
      return {
        success: true,
        message: response.message || 'Application withdrawn successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to withdraw application')
    }
  },

  // Delete application
  async delete(id) {
    try {
      const response = await httpClient.delete(ENDPOINTS.APPLICATIONS.DELETE(id))
      return {
        success: true,
        message: response.message || 'Application deleted successfully'
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to delete application')
    }
  },

  // Get applications for a specific job (for employers)
  async getByJob(jobId, params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.APPLICATIONS.BY_JOB(jobId), { params })
      return {
        success: true,
        data: response.applications || response.data || [],
        pagination: response.pagination,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch job applications')
    }
  }
}