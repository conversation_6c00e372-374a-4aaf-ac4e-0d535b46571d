// Alert Manager Utility for PrimeVue Dialogs
import { createApp, h } from 'vue'
import Dialog from 'primevue/dialog'
import <PERSON><PERSON> from 'primevue/button'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'

class AlertManager {
  constructor() {
    this.activeDialogs = new Set()
  }

  // Show error dialog
  showError(title, message, options = {}) {
    return this._showDialog({
      type: 'error',
      title: title || 'Error',
      message,
      icon: 'pi pi-times-circle',
      iconColor: '#ef4444',
      confirmText: options.confirmText || 'OK',
      ...options
    })
  }

  // Show success dialog
  showSuccess(title, message, options = {}) {
    return this._showDialog({
      type: 'success',
      title: title || 'Success',
      message,
      icon: 'pi pi-check-circle',
      iconColor: '#10b981',
      confirmText: options.confirmText || 'OK',
      ...options
    })
  }

  // Show warning dialog
  showWarning(title, message, options = {}) {
    return this._showDialog({
      type: 'warning',
      title: title || 'Warning',
      message,
      icon: 'pi pi-exclamation-triangle',
      iconColor: '#f59e0b',
      confirmText: options.confirmText || 'OK',
      ...options
    })
  }

  // Show info dialog
  showInfo(title, message, options = {}) {
    return this._showDialog({
      type: 'info',
      title: title || 'Information',
      message,
      icon: 'pi pi-info-circle',
      iconColor: '#3b82f6',
      confirmText: options.confirmText || 'OK',
      ...options
    })
  }

  // Show confirmation dialog
  showConfirm(title, message, options = {}) {
    return this._showDialog({
      type: 'confirm',
      title: title || 'Confirm',
      message,
      icon: 'pi pi-question-circle',
      iconColor: '#6b7280',
      confirmText: options.confirmText || 'Yes',
      cancelText: options.cancelText || 'No',
      showCancel: true,
      ...options
    })
  }

  // Show OTP expired dialog
  showOtpExpired(options = {}) {
    return this.showError(
      'OTP Expired',
      'Your OTP has expired. Please request a new one.',
      {
        confirmText: 'Request New OTP',
        ...options
      }
    )
  }

  // Show invalid OTP dialog
  showInvalidOtp(options = {}) {
    return this.showError(
      'Invalid OTP',
      'The OTP you entered is incorrect. Please check and try again.',
      {
        confirmText: 'Try Again',
        ...options
      }
    )
  }

  // Private method to create and show dialog
  _showDialog(config) {
    return new Promise((resolve) => {
      // Create a container for the dialog
      const container = document.createElement('div')
      document.body.appendChild(container)

      // Track this dialog
      this.activeDialogs.add(container)

      // Create Vue app instance for the dialog
      const app = createApp({
        data() {
          return {
            visible: true
          }
        },
        methods: {
          onConfirm() {
            this.visible = false
            this.cleanup()
            resolve(true)
          },
          onCancel() {
            this.visible = false
            this.cleanup()
            resolve(false)
          },
          onHide() {
            this.cleanup()
            resolve(false)
          },
          cleanup() {
            setTimeout(() => {
              if (container && container.parentNode) {
                app.unmount()
                container.parentNode.removeChild(container)
                this.activeDialogs.delete(container)
              }
            }, 300) // Wait for dialog close animation
          }
        },
        render() {
          return h(Dialog, {
            visible: this.visible,
            'onUpdate:visible': (value) => {
              this.visible = value
              if (!value) this.onHide()
            },
            modal: true,
            closable: config.closable !== false,
            closeOnEscape: config.closeOnEscape !== false,
            dismissableMask: config.dismissableMask !== false,
            header: config.title,
            style: { 
              width: config.width || '400px',
              maxWidth: '90vw'
            },
            class: `alert-dialog alert-dialog--${config.type}`,
            pt: {
              root: { class: 'alert-dialog-root' },
              header: { class: 'alert-dialog-header' },
              content: { class: 'alert-dialog-content' },
              footer: { class: 'alert-dialog-footer' }
            }
          }, {
            default: () => [
              h('div', { class: 'alert-dialog-body' }, [
                // Icon
                config.icon && h('div', { class: 'alert-dialog-icon' }, [
                  h('i', { 
                    class: config.icon,
                    style: { 
                      color: config.iconColor,
                      fontSize: '3rem'
                    }
                  })
                ]),
                // Message
                h('div', { class: 'alert-dialog-message' }, [
                  h('p', config.message)
                ])
              ])
            ],
            footer: () => [
              h('div', { class: 'alert-dialog-actions' }, [
                // Cancel button (for confirm dialogs)
                config.showCancel && h(Button, {
                  label: config.cancelText,
                  severity: 'secondary',
                  outlined: true,
                  onClick: this.onCancel,
                  class: 'alert-dialog-cancel'
                }),
                // Confirm button
                h(Button, {
                  label: config.confirmText,
                  severity: config.type === 'error' ? 'danger' : 
                           config.type === 'success' ? 'success' :
                           config.type === 'warning' ? 'warning' : 'primary',
                  onClick: this.onConfirm,
                  class: 'alert-dialog-confirm',
                  autofocus: true
                })
              ])
            ]
          })
        }
      })

      // Configure PrimeVue for the dialog app
      app.use(PrimeVue, {
        theme: {
          preset: Aura,
          options: {
            prefix: 'p',
            darkModeSelector: '.dark-theme',
            cssLayer: false
          }
        }
      })

      // Mount the dialog
      app.mount(container)
    })
  }

  // Close all active dialogs
  closeAll() {
    this.activeDialogs.forEach(container => {
      if (container && container.parentNode) {
        container.parentNode.removeChild(container)
      }
    })
    this.activeDialogs.clear()
  }
}

// Create singleton instance
const alertManager = new AlertManager()

// Export singleton instance and class
export default alertManager
export { AlertManager }