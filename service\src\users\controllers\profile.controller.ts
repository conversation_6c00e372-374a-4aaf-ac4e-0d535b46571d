import {
  Controller,
  Get,
  Put,
  Body,
  UseGuards,
  Request,
  Post,
  Delete,
  UseInterceptors,
  UploadedFile,
  MaxFileSizeValidator,
  FileTypeValidator,
  ParseFilePipe,
  Query,
  HttpStatus,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { ProfileService } from '../services/profile.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { ProfileEntity } from '../entities/profile.entity';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';

@ApiTags('Profile')
@Controller('profile')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get()
  @ApiOperation({ summary: 'Get own profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully', type: ApiResponseDto })
  async getMyProfile(@Request() req): Promise<ApiResponseDto<ProfileEntity>> {
    try {
      const profile = await this.profileService.getByUserId(req.user.id);
      return ApiResponseDto.success(profile, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('PROFILE_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Put()
  @ApiOperation({ summary: 'Update own profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully', type: ApiResponseDto })
  async updateProfile(
    @Request() req,
    @Body() profileData: Partial<ProfileEntity>,
  ): Promise<ApiResponseDto<ProfileEntity>> {
    try {
      const updatedProfile = await this.profileService.update(req.user.id, profileData, req.user.id);
      return ApiResponseDto.success(updatedProfile, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('PROFILE_UPDATE_FAILED', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Put('complete')
  @ApiOperation({ summary: 'Mark profile as complete' })
  @ApiResponse({ status: 200, description: 'Profile marked as complete', type: ApiResponseDto })
  async completeProfile(@Request() req): Promise<ApiResponseDto<ProfileEntity>> {
    try {
      const completedProfile = await this.profileService.markAsComplete(req.user.id);
      return ApiResponseDto.success(completedProfile, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('PROFILE_COMPLETE_FAILED', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('image')
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
          description: 'Profile image file (JPEG, PNG, or WebP)',
        },
      },
    },
  })
  @ApiQuery({
    name: 'crop',
    required: false,
    type: Boolean,
    description: 'Whether to crop the image to a square',
  })
  @ApiOperation({ summary: 'Upload profile image' })
  @ApiResponse({ status: 201, description: 'Profile image uploaded successfully', type: ApiResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid file type or size', type: ApiResponseDto })
  async uploadProfileImage(
    @Request() req,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: /(jpg|jpeg|png|webp)$/ }),
        ],
      }),
    )
    file: Express.Multer.File,
    @Query('crop') crop?: string,
  ): Promise<ApiResponseDto<ProfileEntity>> {
    try {
      const updatedProfile = await this.profileService.uploadProfileImage(req.user.id, file, {
        crop: crop === 'true',
      });
      return ApiResponseDto.success(updatedProfile, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error('IMAGE_UPLOAD_FAILED', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete('image')
  @ApiOperation({ summary: 'Delete profile image' })
  @ApiResponse({ status: 200, description: 'Profile image deleted successfully', type: ApiResponseDto })
  async deleteProfileImage(@Request() req): Promise<ApiResponseDto<ProfileEntity>> {
    try {
      const updatedProfile = await this.profileService.deleteProfileImage(req.user.id);
      return ApiResponseDto.success(updatedProfile, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('IMAGE_DELETE_FAILED', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Put('/:userId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update user profile by userId (Admin only)' })
  @ApiParam({
    name: 'userId',
    description: 'ID of the user whose profile to update',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    type: ApiResponseDto,
  })
  async updateUserProfile(
    @Request() req,
    @Param('userId') userId: string,
    @Body() profileData: Partial<ProfileEntity>,
  ): Promise<ApiResponseDto<ProfileEntity>> {
    try {
      const updatedProfile = await this.profileService.update(userId, profileData, req.user.id);
      return ApiResponseDto.success(updatedProfile, HttpStatus.OK);
    } catch (error) {
      if (error.message === 'User not found') {
        return ApiResponseDto.error('USER_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
      }
      return ApiResponseDto.error('PROFILE_UPDATE_FAILED', error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
