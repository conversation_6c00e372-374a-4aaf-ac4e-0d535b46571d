<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'


const authStore = useAuthStore()
const router = useRouter()

// onMounted(async () => {
//   await authStore.getEnumData()
//   await authStore.getIndustries()
// })
</script>
<template>
  <router-view />
</template>


<style>
/* Global styles are handled in style.css */
</style>