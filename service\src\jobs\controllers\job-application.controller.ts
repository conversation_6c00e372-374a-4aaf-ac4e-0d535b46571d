import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Query,
  Patch,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse as SwaggerResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { JobApplicationService } from '../services/job-application.service';
import { CreateJobApplicationDto } from '../dto/create-job-application.dto';
import { JobApplicationEntity, ApplicationStatus } from '../entities/job-application.entity';
import { JobEntity } from '../entities/job.entity';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';

@ApiTags('Job Applications')
@Controller('job-applications')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JobApplicationController {
  constructor(private readonly jobApplicationService: JobApplicationService) {}

  @Post('jobs/:jobId/apply')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Apply for a job' })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Job application submitted successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.BAD_REQUEST, description: 'Bad request - Already applied or job is inactive', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden - Job seeker access required', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.NOT_FOUND, description: 'Job not found', type: ApiResponseDto })
  async applyForJob(
    @Param('jobId') jobId: string,
    @Request() req,
    @Body() createApplicationDto: CreateJobApplicationDto,
  ): Promise<ApiResponseDto<JobApplicationEntity>> {
    try {
      const application = await this.jobApplicationService.applyForJob(req.user.id, jobId, createApplicationDto);
      return ApiResponseDto.success(application, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error('JOB_APPLICATION_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('my-applications')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Get all job applications for the authenticated user' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns all job applications',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden - Job seeker access required', type: ApiResponseDto })
  async getMyApplications(
    @Request() req,
    @Query() paginationDto: PaginationDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobApplicationEntity>>> {
    try {
      const applications = await this.jobApplicationService.getMyApplications(req.user.id, paginationDto);
      return ApiResponseDto.success(applications, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('MY_APPLICATIONS_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('jobs/:jobId/favorite')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Toggle job favorite status' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job favorite status toggled successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden - Job seeker access required', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.NOT_FOUND, description: 'Job not found', type: ApiResponseDto })
  async toggleFavorite(
    @Param('jobId') jobId: string,
    @Request() req,
  ): Promise<ApiResponseDto<{ isFavorite: boolean }>> {
    try {
      const result = await this.jobApplicationService.toggleFavorite(req.user.id, jobId);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('FAVORITE_TOGGLE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('my-favorites')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Get all favorite jobs for the authenticated user' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns all favorite jobs',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden - Job seeker access required', type: ApiResponseDto })
  async getMyFavorites(
    @Request() req,
    @Query() paginationDto: PaginationDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobEntity>>> {
    try {
      const favorites = await this.jobApplicationService.getMyFavorites(req.user.id, paginationDto);
      return ApiResponseDto.success(favorites, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('MY_FAVORITES_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Patch(':applicationId/status')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update job application status' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Application status updated successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden - Employer or admin access required', type: ApiResponseDto })
  @SwaggerResponse({ status: HttpStatus.NOT_FOUND, description: 'Application not found', type: ApiResponseDto })
  async updateApplicationStatus(
    @Param('applicationId') applicationId: string,
    @Request() req,
    @Body('status') status: ApplicationStatus,
    @Body('comment') comment?: string,
  ): Promise<ApiResponseDto<JobApplicationEntity>> {
    try {
      const application = await this.jobApplicationService.updateApplicationStatus(
        applicationId,
        req.user.id,
        req.user.role,
        status,
        comment,
      );
      return ApiResponseDto.success(application, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('APPLICATION_STATUS_UPDATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
