// Mock API for Industries endpoint
// GET: /industries

const delay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms))

export const industriesMock = {
  async getIndustries() {
    await delay(300)
    
    const industries = [
    {
        "id": "65eacd40-0c09-4f85-931f-43add0b86aaf",
        "createdAt": "2025-06-08T11:38:45.516Z",
        "updatedAt": "2025-06-08T11:38:45.516Z",
        "name": "Restaurent",
        "description": "string",
        "isActive": true,
        "subIndustries": [
            {
                "id": "403cc02b-07d7-4a40-b30d-ff7e39abbed1",
                "createdAt": "2025-06-08T11:39:24.715Z",
                "updatedAt": "2025-06-08T11:39:24.715Z",
                "name": "Cleaner",
                "description": "Cleaner",
                "isActive": true,
                "industryId": "65eacd40-0c09-4f85-931f-43add0b86aaf"
            }
        ]
    }
]
    
    return {
      data: industries,
      success: true,
      status: 200,
      message: 'Industries retrieved successfully'
    }
  }
}