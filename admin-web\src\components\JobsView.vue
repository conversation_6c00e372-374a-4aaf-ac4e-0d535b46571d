<script setup>
import { ref, reactive, computed } from 'vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dialog from 'primevue/dialog'
import Dropdown from 'primevue/dropdown'
import InputNumber from 'primevue/inputnumber'
import MultiSelect from 'primevue/multiselect'
import Checkbox from 'primevue/checkbox'
import Tag from 'primevue/tag'
import Chips from 'primevue/chips'
import ConfirmDialog from 'primevue/confirmdialog'
import Sidebar from 'primevue/sidebar'
import { useConfirm } from 'primevue/useconfirm'

const confirm = useConfirm()

// Enums based on the DTO
const JobType = {
  FULL_TIME: 'FULL_TIME',
  PART_TIME: 'PART_TIME',
  CONTRACT: 'CONTRACT',
  DAILY_WAGE: 'DAILY_WAGE'
}

const PaymentType = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  BI_WEEKLY: 'BI_WEEKLY',
  MONTHLY: 'MONTHLY'
}

const JobUrgency = {
  FLEXIBLE: 'FLEXIBLE',
  URGENT: 'URGENT',
  IMMEDIATE: 'IMMEDIATE'
}

const ExperienceLevel = {
  FRESHER: 'FRESHER',
  ENTRY_LEVEL: 'ENTRY_LEVEL',
  MID_LEVEL: 'MID_LEVEL',
  SENIOR_LEVEL: 'SENIOR_LEVEL',
  EXPERT: 'EXPERT'
}

const ContactDisplayType = {
  NONE: 'NONE',
  PHONE: 'PHONE',
  EMAIL: 'EMAIL',
  BOTH: 'BOTH'
}

// Sample industries data (would come from the industries store/API)
const industries = ref([
  { id: '1', name: 'Technology', label: 'Technology' },
  { id: '2', name: 'Healthcare', label: 'Healthcare' },
  { id: '3', name: 'Finance', label: 'Finance' },
  { id: '4', name: 'Construction', label: 'Construction' },
  { id: '5', name: 'Manufacturing', label: 'Manufacturing' }
])

// Sample jobs data
const jobs = ref([
  {
    id: '1',
    title: 'Senior Software Developer',
    description: 'We are looking for an experienced software developer to join our team...',
    industryId: '1',
    industryName: 'Technology',
    salary: 75000,
    jobType: JobType.FULL_TIME,
    paymentType: PaymentType.MONTHLY,
    location: 'New York, NY',
    urgency: JobUrgency.FLEXIBLE,
    experienceLevel: ExperienceLevel.SENIOR_LEVEL,
    benefits: ['Health Insurance', 'Dental Coverage', '401k Matching'],
    requirements: ['Bachelor\'s Degree', '5+ years experience', 'JavaScript proficiency'],
    responsibilities: ['Code development', 'Code review', 'Mentoring junior developers'],
    skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
    thumbnail: 'https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [],
    showContact: true,
    contactDisplayType: ContactDisplayType.EMAIL,
    contactPhone: '******-0123',
    contactEmail: '<EMAIL>',
    contactPerson: 'John Smith',
    vacancies: 2,
    workingHours: '40 hours per week, Monday to Friday',
    accommodation: null,
    transportation: 'Transportation allowance provided',
    foodProvided: 'Lunch provided',
    safetyEquipment: null,
    trainingProvided: 'Comprehensive onboarding program',
    createdAt: new Date('2024-01-15'),
    isActive: true
  },
  {
    id: '2',
    title: 'Construction Worker',
    description: 'Seeking experienced construction workers for residential projects...',
    industryId: '4',
    industryName: 'Construction',
    salary: 25,
    jobType: JobType.DAILY_WAGE,
    paymentType: PaymentType.DAILY,
    location: 'Los Angeles, CA',
    urgency: JobUrgency.URGENT,
    experienceLevel: ExperienceLevel.MID_LEVEL,
    benefits: ['Overtime Pay', 'Safety Equipment', 'Weekly Off'],
    requirements: ['Valid Driver License', 'Safety Training Certificate', 'Physical fitness'],
    responsibilities: ['Operating heavy machinery', 'Following safety protocols', 'Site cleanup'],
    skills: ['Heavy Equipment Operation', 'Safety Protocols', 'Blueprint Reading'],
    thumbnail: 'https://images.pexels.com/photos/1216589/pexels-photo-1216589.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [],
    showContact: true,
    contactDisplayType: ContactDisplayType.PHONE,
    contactPhone: '******-0456',
    contactEmail: '<EMAIL>',
    contactPerson: 'Mike Johnson',
    vacancies: 5,
    workingHours: '8 hours per day, 6 days a week',
    accommodation: 'Shared accommodation available',
    transportation: 'Company transport provided',
    foodProvided: 'Meals provided',
    safetyEquipment: 'Safety boots, helmet, and gloves provided',
    trainingProvided: 'On-site safety training',
    createdAt: new Date('2024-01-20'),
    isActive: true
  }
])

// Dialog states
const showJobDialog = ref(false)
const isEditMode = ref(false)
const selectedJob = ref(null)

// Filter states
const showFilters = ref(false)
const filtersCollapsed = ref(false)

// Form data
const jobForm = reactive({
  title: '',
  description: '',
  industryId: '',
  salary: null,
  jobType: JobType.FULL_TIME,
  paymentType: PaymentType.MONTHLY,
  location: '',
  urgency: JobUrgency.FLEXIBLE,
  experienceLevel: ExperienceLevel.FRESHER,
  benefits: [],
  requirements: [],
  responsibilities: [],
  skills: [],
  thumbnail: '',
  images: [],
  showContact: true,
  contactDisplayType: ContactDisplayType.NONE,
  contactPhone: '',
  contactEmail: '',
  contactPerson: '',
  vacancies: 1,
  workingHours: '',
  accommodation: '',
  transportation: '',
  foodProvided: '',
  safetyEquipment: '',
  trainingProvided: ''
})

// Search and filters
const searchTerm = ref('')
const selectedIndustryFilter = ref(null)
const selectedJobTypeFilter = ref(null)
const selectedUrgencyFilter = ref(null)

// Options for dropdowns
const jobTypeOptions = Object.values(JobType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const paymentTypeOptions = Object.values(PaymentType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const urgencyOptions = Object.values(JobUrgency).map(urgency => ({
  label: urgency.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: urgency
}))

const experienceLevelOptions = Object.values(ExperienceLevel).map(level => ({
  label: level.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: level
}))

const contactDisplayTypeOptions = Object.values(ContactDisplayType).map(type => ({
  label: type.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

// Computed properties
const filteredJobs = computed(() => {
  let filtered = jobs.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(job => 
      job.title.toLowerCase().includes(search) ||
      job.description.toLowerCase().includes(search) ||
      job.location?.toLowerCase().includes(search) ||
      job.skills.some(skill => skill.toLowerCase().includes(search))
    )
  }

  if (selectedIndustryFilter.value) {
    filtered = filtered.filter(job => job.industryId === selectedIndustryFilter.value)
  }

  if (selectedJobTypeFilter.value) {
    filtered = filtered.filter(job => job.jobType === selectedJobTypeFilter.value)
  }

  if (selectedUrgencyFilter.value) {
    filtered = filtered.filter(job => job.urgency === selectedUrgencyFilter.value)
  }

  return filtered
})

const industryFilterOptions = computed(() => 
  industries.value.map(industry => ({
    label: industry.name,
    value: industry.id
  }))
)

const jobStats = computed(() => {
  const total = jobs.value.length
  const active = jobs.value.filter(j => j.isActive).length
  const byType = jobs.value.reduce((acc, job) => {
    acc[job.jobType] = (acc[job.jobType] || 0) + 1
    return acc
  }, {})
  const byUrgency = jobs.value.reduce((acc, job) => {
    acc[job.urgency] = (acc[job.urgency] || 0) + 1
    return acc
  }, {})
  
  return {
    total,
    active,
    inactive: total - active,
    byType,
    byUrgency
  }
})

// Methods
const openJobDialog = (job = null) => {
  isEditMode.value = !!job
  if (job) {
    // Populate form with job data
    Object.keys(jobForm).forEach(key => {
      if (job[key] !== undefined) {
        jobForm[key] = job[key]
      }
    })
    selectedJob.value = job
  } else {
    resetJobForm()
  }
  showJobDialog.value = true
}

const resetJobForm = () => {
  Object.assign(jobForm, {
    title: '',
    description: '',
    industryId: '',
    salary: null,
    jobType: JobType.FULL_TIME,
    paymentType: PaymentType.MONTHLY,
    location: '',
    urgency: JobUrgency.FLEXIBLE,
    experienceLevel: ExperienceLevel.FRESHER,
    benefits: [],
    requirements: [],
    responsibilities: [],
    skills: [],
    thumbnail: '',
    images: [],
    showContact: true,
    contactDisplayType: ContactDisplayType.NONE,
    contactPhone: '',
    contactEmail: '',
    contactPerson: '',
    vacancies: 1,
    workingHours: '',
    accommodation: '',
    transportation: '',
    foodProvided: '',
    safetyEquipment: '',
    trainingProvided: ''
  })
  selectedJob.value = null
}

const saveJob = () => {
  if (!jobForm.title.trim() || !jobForm.description.trim() || !jobForm.industryId) return

  const industryName = industries.value.find(i => i.id === jobForm.industryId)?.name || ''

  if (isEditMode.value && selectedJob.value) {
    // Update existing job
    const index = jobs.value.findIndex(j => j.id === selectedJob.value.id)
    if (index !== -1) {
      jobs.value[index] = {
        ...jobs.value[index],
        ...jobForm,
        industryName,
        id: selectedJob.value.id,
        createdAt: jobs.value[index].createdAt,
        isActive: jobs.value[index].isActive
      }
    }
  } else {
    // Add new job
    const newJob = {
      ...jobForm,
      id: Date.now().toString(),
      industryName,
      createdAt: new Date(),
      isActive: true
    }
    jobs.value.push(newJob)
  }

  showJobDialog.value = false
  resetJobForm()
}

const deleteJob = (job) => {
  confirm.require({
    message: `Are you sure you want to delete the job "${job.title}"?`,
    header: 'Delete Job',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const index = jobs.value.findIndex(j => j.id === job.id)
      if (index !== -1) {
        jobs.value.splice(index, 1)
      }
    }
  })
}

const toggleJobStatus = (job) => {
  const index = jobs.value.findIndex(j => j.id === job.id)
  if (index !== -1) {
    jobs.value[index].isActive = !jobs.value[index].isActive
  }
}

const getJobTypeColor = (jobType) => {
  const colors = {
    [JobType.FULL_TIME]: 'success',
    [JobType.PART_TIME]: 'info',
    [JobType.CONTRACT]: 'warning',
    [JobType.DAILY_WAGE]: 'secondary'
  }
  return colors[jobType] || 'info'
}

const getUrgencyColor = (urgency) => {
  const colors = {
    [JobUrgency.FLEXIBLE]: 'success',
    [JobUrgency.URGENT]: 'warning',
    [JobUrgency.IMMEDIATE]: 'danger'
  }
  return colors[urgency] || 'info'
}

const formatSalary = (job) => {
  const amount = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(job.salary)

  const frequency = job.paymentType.toLowerCase().replace('_', ' ')
  return `${amount}/${frequency}`
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

const clearFilters = () => {
  searchTerm.value = ''
  selectedIndustryFilter.value = null
  selectedJobTypeFilter.value = null
  selectedUrgencyFilter.value = null
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const toggleFiltersCollapse = () => {
  filtersCollapsed.value = !filtersCollapsed.value
}
</script>

<template>
  <div class="jobs-view">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <Button
            icon="pi pi-filter"
            text
            rounded
            @click="toggleFilters"
            class="filter-toggle mobile-only"
            aria-label="Toggle filters"
            v-tooltip="'Toggle Filters'"
          />
          <div class="header-title">
            <h1>Jobs</h1>
            <p>Manage job postings and applications</p>
          </div>
        </div>
        <div class="header-actions">
          <Button 
            label="Add Job" 
            icon="pi pi-plus" 
            @click="openJobDialog()"
          />
        </div>
      </div>
    </div>

    <div class="content-layout">
      <!-- Left Sidebar with Filters (Desktop) -->
      <div :class="['filters-sidebar', 'desktop-only', { collapsed: filtersCollapsed }]">
        <div class="filters-header">
          <h3 v-if="!filtersCollapsed">
            <i class="pi pi-filter"></i>
            Filters
          </h3>
          <Button 
            :icon="filtersCollapsed ? 'pi pi-angle-right' : 'pi pi-angle-left'"
            text
            rounded
            size="small"
            @click="toggleFiltersCollapse"
            v-tooltip="filtersCollapsed ? 'Expand Filters' : 'Collapse Filters'"
            class="collapse-btn"
          />
        </div>

        <div v-if="!filtersCollapsed" class="filters-content">
          <!-- Search -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-search"></i>
              Search
            </label>
            <InputText 
              v-model="searchTerm" 
              placeholder="Search jobs..." 
              class="w-full"
            />
          </div>

          <!-- Industry Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-building"></i>
              Industry
            </label>
            <Dropdown
              v-model="selectedIndustryFilter"
              :options="industryFilterOptions"
              option-label="label"
              option-value="value"
              placeholder="Select industry"
              :showClear="true"
              class="w-full"
            />
          </div>

          <!-- Job Type Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-briefcase"></i>
              Job Type
            </label>
            <Dropdown
              v-model="selectedJobTypeFilter"
              :options="jobTypeOptions"
              option-label="label"
              option-value="value"
              placeholder="Select job type"
              :showClear="true"
              class="w-full"
            />
          </div>

          <!-- Urgency Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-clock"></i>
              Urgency
            </label>
            <Dropdown
              v-model="selectedUrgencyFilter"
              :options="urgencyOptions"
              option-label="label"
              option-value="value"
              placeholder="Select urgency"
              :showClear="true"
              class="w-full"
            />
          </div>

          <!-- Statistics -->
          <div class="filter-stats">
            <h4>Statistics</h4>
            <div class="stats-list">
              <div class="stat-item">
                <span class="stat-label">Total Jobs</span>
                <span class="stat-value">{{ jobStats.total }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Active</span>
                <span class="stat-value success">{{ jobStats.active }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Full Time</span>
                <span class="stat-value">{{ jobStats.byType.FULL_TIME || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Urgent</span>
                <span class="stat-value warning">{{ jobStats.byUrgency.URGENT || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- Clear Filters -->
          <Button 
            label="Clear All Filters"
            icon="pi pi-filter-slash" 
            severity="secondary"
            @click="clearFilters"
            :disabled="!searchTerm && !selectedIndustryFilter && !selectedJobTypeFilter && !selectedUrgencyFilter"
            class="w-full"
          />
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="main-content">
        <!-- Jobs Table -->
        <div class="table-container">
          <DataTable 
            :value="filteredJobs" 
            class="jobs-table"
            :paginator="true" 
            :rows="20"
            :rows-per-page-options="[10, 20, 50]"
            stripedRows
            :rowHover="true"
            sort-field="createdAt"
            :sort-order="-1"
            :scrollable="true"
            scroll-height="flex"
          >
            <Column field="title" header="Job Title" sortable>
              <template #body="slotProps">
                <div class="job-title-cell">
                  <div class="job-thumbnail" v-if="slotProps.data.thumbnail">
                    <img :src="slotProps.data.thumbnail" :alt="slotProps.data.title" />
                  </div>
                  <div class="job-info">
                    <span class="job-title">{{ slotProps.data.title }}</span>
                    <span class="job-industry">{{ slotProps.data.industryName }}</span>
                  </div>
                </div>
              </template>
            </Column>
            
            <Column field="location" header="Location" sortable>
              <template #body="slotProps">
                <span class="location-text">{{ slotProps.data.location || 'Remote' }}</span>
              </template>
            </Column>
            
            <Column field="salary" header="Salary" sortable>
              <template #body="slotProps">
                <span class="salary-text">{{ formatSalary(slotProps.data) }}</span>
              </template>
            </Column>
            
            <Column field="jobType" header="Type" sortable>
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.jobType.replace(/_/g, ' ')" 
                  :severity="getJobTypeColor(slotProps.data.jobType)"
                />
              </template>
            </Column>
            
            <Column field="urgency" header="Urgency" sortable>
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.urgency" 
                  :severity="getUrgencyColor(slotProps.data.urgency)"
                />
              </template>
            </Column>
            
            <Column field="vacancies" header="Vacancies" sortable>
              <template #body="slotProps">
                <span class="vacancies-count">{{ slotProps.data.vacancies || 1 }}</span>
              </template>
            </Column>
            
            <Column field="createdAt" header="Posted" sortable>
              <template #body="slotProps">
                <span class="posted-date">{{ formatDate(slotProps.data.createdAt) }}</span>
              </template>
            </Column>
            
            <Column field="isActive" header="Status" sortable>
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.isActive ? 'Active' : 'Inactive'" 
                  :severity="slotProps.data.isActive ? 'success' : 'secondary'"
                />
              </template>
            </Column>
            
            <Column header="Actions">
              <template #body="slotProps">
                <div class="action-buttons">
                  <Button 
                    icon="pi pi-pencil" 
                    text 
                    rounded 
                    size="small" 
                    @click="openJobDialog(slotProps.data)"
                    v-tooltip="'Edit Job'"
                  />
                  <Button 
                    :icon="slotProps.data.isActive ? 'pi pi-eye-slash' : 'pi pi-eye'" 
                    text 
                    rounded 
                    size="small" 
                    :severity="slotProps.data.isActive ? 'warning' : 'success'"
                    @click="toggleJobStatus(slotProps.data)"
                    :v-tooltip="slotProps.data.isActive ? 'Deactivate' : 'Activate'"
                  />
                  <Button 
                    icon="pi pi-trash" 
                    text 
                    rounded 
                    size="small" 
                    severity="danger" 
                    @click="deleteJob(slotProps.data)"
                    v-tooltip="'Delete Job'"
                  />
                </div>
              </template>
            </Column>
          </DataTable>
        </div>
      </div>
    </div>

    <!-- Mobile Filter Drawer -->
    <Sidebar 
      v-model:visible="showFilters" 
      position="left" 
      class="filter-drawer mobile-only"
      :style="{ width: '320px' }"
    >
      <template #header>
        <div class="drawer-header">
          <h3>
            <i class="pi pi-filter"></i>
            Filters
          </h3>
        </div>
      </template>

      <div class="drawer-content">
        <!-- Search -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-search"></i>
            Search
          </label>
          <InputText 
            v-model="searchTerm" 
            placeholder="Search jobs..." 
            class="w-full"
          />
        </div>

        <!-- Industry Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-building"></i>
            Industry
          </label>
          <Dropdown
            v-model="selectedIndustryFilter"
            :options="industryFilterOptions"
            option-label="label"
            option-value="value"
            placeholder="Select industry"
            :showClear="true"
            class="w-full"
          />
        </div>

        <!-- Job Type Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-briefcase"></i>
            Job Type
          </label>
          <Dropdown
            v-model="selectedJobTypeFilter"
            :options="jobTypeOptions"
            option-label="label"
            option-value="value"
            placeholder="Select job type"
            :showClear="true"
            class="w-full"
          />
        </div>

        <!-- Urgency Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-clock"></i>
            Urgency
          </label>
          <Dropdown
            v-model="selectedUrgencyFilter"
            :options="urgencyOptions"
            option-label="label"
            option-value="value"
            placeholder="Select urgency"
            :showClear="true"
            class="w-full"
          />
        </div>

        <!-- Statistics -->
        <div class="filter-stats">
          <h4>Statistics</h4>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">Total Jobs</span>
              <span class="stat-value">{{ jobStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active</span>
              <span class="stat-value success">{{ jobStats.active }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Full Time</span>
              <span class="stat-value">{{ jobStats.byType.FULL_TIME || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Urgent</span>
              <span class="stat-value warning">{{ jobStats.byUrgency.URGENT || 0 }}</span>
            </div>
          </div>
        </div>

        <!-- Clear Filters -->
        <Button 
          label="Clear All Filters"
          icon="pi pi-filter-slash" 
          severity="secondary"
          @click="clearFilters"
          :disabled="!searchTerm && !selectedIndustryFilter && !selectedJobTypeFilter && !selectedUrgencyFilter"
          class="w-full"
        />
      </div>
    </Sidebar>

    <!-- Job Dialog -->
    <Dialog 
      v-model:visible="showJobDialog" 
      :header="isEditMode ? 'Edit Job' : 'Add Job'"
      :modal="true" 
      class="job-dialog"
      :style="{ width: '900px' }"
      :maximizable="true"
    >
      <div class="dialog-content">
        <div class="form-grid">
          <!-- Basic Information -->
          <div class="form-section">
            <h4>Basic Information</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-title">Job Title *</label>
                <InputText 
                  id="job-title"
                  v-model="jobForm.title" 
                  placeholder="Enter job title"
                  class="w-full"
                  :class="{ 'p-invalid': !jobForm.title.trim() }"
                />
              </div>
              
              <div class="form-group">
                <label for="job-industry">Industry *</label>
                <Dropdown
                  id="job-industry"
                  v-model="jobForm.industryId"
                  :options="industries"
                  option-label="name"
                  option-value="id"
                  placeholder="Select an industry"
                  class="w-full"
                  :class="{ 'p-invalid': !jobForm.industryId }"
                />
              </div>
            </div>
            
            <div class="form-group">
              <label for="job-description">Description *</label>
              <Textarea 
                id="job-description"
                v-model="jobForm.description" 
                placeholder="Enter job description"
                rows="4"
                class="w-full"
                :class="{ 'p-invalid': !jobForm.description.trim() }"
              />
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-location">Location</label>
                <InputText 
                  id="job-location"
                  v-model="jobForm.location" 
                  placeholder="Enter job location"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="job-vacancies">Number of Vacancies</label>
                <InputNumber 
                  id="job-vacancies"
                  v-model="jobForm.vacancies" 
                  :min="1"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Job Details -->
          <div class="form-section">
            <h4>Job Details</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-type">Job Type</label>
                <Dropdown
                  id="job-type"
                  v-model="jobForm.jobType"
                  :options="jobTypeOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="payment-type">Payment Type</label>
                <Dropdown
                  id="payment-type"
                  v-model="jobForm.paymentType"
                  :options="paymentTypeOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-salary">Salary *</label>
                <InputNumber 
                  id="job-salary"
                  v-model="jobForm.salary" 
                  mode="currency"
                  currency="USD"
                  :min="0"
                  class="w-full"
                  :class="{ 'p-invalid': !jobForm.salary }"
                />
              </div>
              
              <div class="form-group">
                <label for="job-urgency">Urgency</label>
                <Dropdown
                  id="job-urgency"
                  v-model="jobForm.urgency"
                  :options="urgencyOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="experience-level">Experience Level</label>
                <Dropdown
                  id="experience-level"
                  v-model="jobForm.experienceLevel"
                  :options="experienceLevelOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="working-hours">Working Hours</label>
                <InputText 
                  id="working-hours"
                  v-model="jobForm.workingHours" 
                  placeholder="e.g., 8 hours per day, 6 days a week"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Job Arrays -->
          <div class="form-section">
            <h4>Job Requirements & Details</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-skills">Skills</label>
                <Chips 
                  id="job-skills"
                  v-model="jobForm.skills" 
                  placeholder="Add skills and press Enter"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="job-benefits">Benefits</label>
                <Chips 
                  id="job-benefits"
                  v-model="jobForm.benefits" 
                  placeholder="Add benefits and press Enter"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-requirements">Requirements</label>
                <Chips 
                  id="job-requirements"
                  v-model="jobForm.requirements" 
                  placeholder="Add requirements and press Enter"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="job-responsibilities">Responsibilities</label>
                <Chips 
                  id="job-responsibilities"
                  v-model="jobForm.responsibilities" 
                  placeholder="Add responsibilities and press Enter"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="form-section">
            <h4>Additional Information</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-thumbnail">Thumbnail URL</label>
                <InputText 
                  id="job-thumbnail"
                  v-model="jobForm.thumbnail" 
                  placeholder="Enter thumbnail image URL"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="job-accommodation">Accommodation</label>
                <InputText 
                  id="job-accommodation"
                  v-model="jobForm.accommodation" 
                  placeholder="Accommodation details"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-transportation">Transportation</label>
                <InputText 
                  id="job-transportation"
                  v-model="jobForm.transportation" 
                  placeholder="Transportation details"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="job-food">Food Provided</label>
                <InputText 
                  id="job-food"
                  v-model="jobForm.foodProvided" 
                  placeholder="Food provision details"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="job-safety">Safety Equipment</label>
                <InputText 
                  id="job-safety"
                  v-model="jobForm.safetyEquipment" 
                  placeholder="Safety equipment provided"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="job-training">Training Provided</label>
                <InputText 
                  id="job-training"
                  v-model="jobForm.trainingProvided" 
                  placeholder="Training details"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="form-section">
            <h4>Contact Information</h4>
            
            <div class="form-row">
              <div class="form-group checkbox-group">
                <Checkbox 
                  id="show-contact"
                  v-model="jobForm.showContact" 
                  :binary="true"
                />
                <label for="show-contact">Show Contact Information</label>
              </div>
              
              <div class="form-group">
                <label for="contact-display-type">Contact Display Type</label>
                <Dropdown
                  id="contact-display-type"
                  v-model="jobForm.contactDisplayType"
                  :options="contactDisplayTypeOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                  :disabled="!jobForm.showContact"
                />
              </div>
            </div>
            
            <div class="form-row" v-if="jobForm.showContact">
              <div class="form-group">
                <label for="contact-person">Contact Person</label>
                <InputText 
                  id="contact-person"
                  v-model="jobForm.contactPerson" 
                  placeholder="Contact person name"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="contact-email">Contact Email</label>
                <InputText 
                  id="contact-email"
                  v-model="jobForm.contactEmail" 
                  type="email"
                  placeholder="Contact email address"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row" v-if="jobForm.showContact">
              <div class="form-group">
                <label for="contact-phone">Contact Phone</label>
                <InputText 
                  id="contact-phone"
                  v-model="jobForm.contactPhone" 
                  placeholder="Contact phone number"
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showJobDialog = false"
          />
          <Button 
            :label="isEditMode ? 'Update' : 'Create'" 
            @click="saveJob"
            :disabled="!jobForm.title.trim() || !jobForm.description.trim() || !jobForm.industryId || !jobForm.salary"
          />
        </div>
      </template>
    </Dialog>

    <ConfirmDialog />
  </div>
</template>

<style scoped>
.jobs-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Page Header */
.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-toggle {
  display: none;
}

.header-title h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-title p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* Content Layout */
.content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Left Filters Sidebar (Desktop) */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease;
}

.filters-sidebar.collapsed {
  width: 60px;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collapse-btn {
  flex-shrink: 0;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.stat-value.success {
  color: var(--p-green-600);
}

.stat-value.warning {
  color: var(--p-yellow-600);
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Table Container */
.table-container {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.jobs-table {
  flex: 1;
  height: 100%;
}

.job-title-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.job-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.job-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.job-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.job-title {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.job-industry {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.location-text,
.salary-text,
.vacancies-count,
.posted-date {
  font-size: 0.875rem;
  color: var(--p-text-color);
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

/* Mobile Filter Drawer */
.filter-drawer {
  z-index: 1000;
}

.drawer-header {
  padding: 1rem 0;
}

.drawer-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.drawer-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Dialog Styling */
.job-dialog {
  max-height: 90vh;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem 0;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  border-bottom: 1px solid var(--p-surface-border);
  padding-bottom: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Responsive Design */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
  
  .filters-sidebar.collapsed {
    width: 60px;
  }
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: flex;
  }
  
  .filter-toggle {
    display: flex;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .table-container {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .job-title-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar,
.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track,
.drawer-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb,
.drawer-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover,
.drawer-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track,
:global(.dark) .drawer-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb,
:global(.dark) .drawer-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover,
:global(.dark) .drawer-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>