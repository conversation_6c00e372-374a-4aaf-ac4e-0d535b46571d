import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobController } from './controllers/job.controller';
import { JobScheduleController } from './controllers/job-schedule.controller';
import { JobApplicationController } from './controllers/job-application.controller';
import { JobService } from './services/job.service';
import { JobApplicationService } from './services/job-application.service';
import { JobEntity } from './entities/job.entity';
import { JobApplicationEntity } from './entities/job-application.entity';
import { JobFavoriteEntity } from './entities/job-favorite.entity';
import { UserEntity } from '../users/entities/user.entity';
import { IndustryEntity } from '../industries/entities/industry.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobEntity,
      JobApplicationEntity,
      JobFavoriteEntity,
      UserEntity,
      IndustryEntity,
    ]),
  ],
  controllers: [JobController, JobScheduleController, JobApplicationController],
  providers: [JobService, JobApplicationService],
  exports: [JobService, JobApplicationService],
})
export class JobsModule {}
