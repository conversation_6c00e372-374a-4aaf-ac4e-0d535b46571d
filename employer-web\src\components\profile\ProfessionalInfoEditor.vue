<template>
  <Dialog 
    :visible="localVisible"
    @update:visible="localVisible = $event"
    header="Edit Organisation Information"
    :modal="true"
    :closable="true"
    class="professional-info-dialog"
    :style="{ width: '600px' }"
    @hide="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="professional-info-form">
      <div class="form-grid">
        <div class="form-group full-width">
          <label for="name">Organisation Name</label>
          <InputText
            id="name"
            v-model="formData.name"
            placeholder="Enter your organization name"
          />
        </div>

        <div class="form-group full-width">
          <label for="description">About Organization</label>
          <Textarea
            id="description"
            v-model="formData.description"
            rows="4"
            placeholder="Write a brief description about your organization..."
            :maxlength="500"
          />
          <small class="char-count">{{ (formData.description || '').length }}/500 characters</small>
        </div>

        <div class="form-group">
          <label for="foundedYear">Founded Year</label>
          <Calendar
            id="foundedYear"
            v-model="formData.foundedYear"
            dateFormat="dd/mm/yy"
            :showIcon="true"
            :yearRange="'1900:' + new Date().getFullYear()"
            placeholder="Select founding date"
          />
        </div>

        <div class="form-group">
          <label for="industry">Industry</label>
          <Dropdown
            id="industry"
            v-model="formData.industry"
            :options="industryOptions"
            optionLabel="name"
            optionValue="id"
            placeholder="Select Industry"
            showClear
          />
        </div>

        <div class="form-group">
          <label for="companySize">Organisation size</label>
          <InputNumber
            id="companySize"
            v-model="formData.companySize"
            placeholder="Total employee"
          />
        </div>

        <div class="form-group">
          <label for="website">Website</label>
          <InputText
            id="website"
            v-model="formData.website"
            placeholder="e.g., https://www.example.com"
          />
        </div>

        <div class="form-group">
          <label for="registrationNumber">Registration Number</label>
          <InputText
            id="registrationNumber"
            v-model="formData.registrationNumber"
            placeholder="e.g., 1234567890"
          />
        </div>

        <div class="form-group">
          <label for="taxId">Tax ID</label>
          <InputText
            id="taxId"
            v-model="formData.taxId"
            placeholder="e.g., 123-45-6789"
          />
        </div>
      </div>
      <Location v-model="formData.location" />
    </form>

    <template #footer>
      <div class="dialog-footer">
        <Button 
          @click="handleCancel"
          label="Cancel"
          outlined
          :disabled="isSaving"
        />
        <Button 
          @click="handleSubmit"
          label="Save Changes"
          :loading="isSaving"
          class="save-btn"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import Dropdown from 'primevue/dropdown'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import Calendar from 'primevue/calendar'
import alertManager from '@/utils/alertManager'
import Location from '@/components/Location.vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  profileData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'save'])

const isSaving = ref(false)

const formData = ref({
  name: '',
  description: '',
  industry: '',
  companySize: null,
  website: '',
  foundedYear: null,
  registrationNumber: '',
  taxId: '',
  location: {
    address: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    latitude: null,
    longitude: null
  }
})

const localVisible = computed({
  get() {
    return props.visible
  },
  set(value) {
    emit('update:visible', value)
  }
})

const industryOptions = authStore.industries

// Watch for prop changes
watch(() => props.profileData, (newData) => {
  if (newData) {
    formData.value = {
      name: newData.company?.name || '',
      description: newData.company?.description || '',
      industry: newData.company?.industry || '',
      companySize: newData.company?.companySize || null,
      website: newData.company?.website || '',
      foundedYear: newData.company?.foundedYear ? new Date(newData.company.foundedYear) : null,
      registrationNumber: newData.company?.registrationNumber || '',
      taxId: newData.company?.taxId || '',
      location: {
        address: newData.company?.address || '',
        city: newData.company?.city || '',
        state: newData.company?.state || '',
        country: newData.company?.country || '',
        postalCode: newData.company?.postalCode || '',
        latitude: newData.company?.latitude || null,
        longitude: newData.company?.longitude || null
      }
    }
  }
}, { immediate: true, deep: true })

const handleSubmit = async () => {
  isSaving.value = true
  
  try {
    // Transform form data to match company structure
    const companyData = {
      name: formData.value.name,
      description: formData.value.description,
      industry: formData.value.industry,
      companySize: formData.value.companySize,
      website: formData.value.website,
      foundedYear: formData.value.foundedYear ? formData.value.foundedYear.toISOString() : null,
      registrationNumber: formData.value.registrationNumber,
      taxId: formData.value.taxId,
      address: formData.value.location.address,
      city: formData.value.location.city,
      state: formData.value.location.state,
      country: formData.value.location.country,
      postalCode: formData.value.location.postalCode,
      latitude: formData.value.location.latitude,
      longitude: formData.value.location.longitude
    }
    
    emit('save', { company: companyData })
    emit('update:visible', false)
    
    alertManager.showSuccess('Success', 'Company information updated successfully!')
  } catch (error) {
    alertManager.showError('Error', 'Failed to update company information. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.professional-info-dialog {
  border-radius: 12px !important;
}

.professional-info-form {
  padding: 1rem 0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group input,
.form-group .p-dropdown,
.form-group .p-inputnumber,
.form-group .p-inputtextarea,
.form-group .p-calendar {
  width: 100% !important;
}

.char-count {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  text-align: right;
}

.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}
</style>