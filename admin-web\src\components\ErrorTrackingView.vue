<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import Card from 'primevue/card'
import <PERSON><PERSON> from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'
import Dialog from 'primevue/dialog'
import Tag from 'primevue/tag'
import Textarea from 'primevue/textarea'
import Calendar from 'primevue/calendar'
import MultiSelect from 'primevue/multiselect'
import ProgressBar from 'primevue/progressbar'
import Chip from 'primevue/chip'
import { FilterMatchMode, FilterOperator } from '@primevue/core/api';

// Error severity enum
const ErrorSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
}

// Sample error data
const errors = ref([
  {
    id: 1,
    errorMessage: 'TypeError: Cannot read property \'name\' of undefined',
    screen: 'UserProfile',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    url: 'https://localhost:5173/users/profile',
    metadata: { 
      componentState: { userId: '123' },
      stackTrace: 'at UserProfile.vue:45:12\nat processUser (utils.js:23:5)',
      timestamp: '2024-01-15T10:30:00Z'
    },
    severity: ErrorSeverity.HIGH,
    createdAt: new Date('2024-01-15T10:30:00Z'),
    resolved: false,
    occurrences: 3
  },
  {
    id: 2,
    errorMessage: 'Network Error: Failed to fetch user data',
    screen: 'Dashboard',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    url: 'https://localhost:5173/dashboard',
    metadata: { 
      apiEndpoint: '/api/users',
      responseStatus: 500,
      retryAttempts: 2
    },
    severity: ErrorSeverity.CRITICAL,
    createdAt: new Date('2024-01-15T09:15:00Z'),
    resolved: true,
    occurrences: 1
  },
  {
    id: 3,
    errorMessage: 'Validation Error: Email format is invalid',
    screen: 'LoginPage',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
    url: 'https://localhost:5173/login',
    metadata: { 
      formData: { email: 'invalid-email' },
      validationRules: ['email']
    },
    severity: ErrorSeverity.MEDIUM,
    createdAt: new Date('2024-01-14T16:45:00Z'),
    resolved: false,
    occurrences: 7
  },
  {
    id: 4,
    errorMessage: 'Warning: Component mounted without required props',
    screen: 'IndustriesView',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    url: 'https://localhost:5173/industries',
    metadata: { 
      missingProps: ['industryId'],
      componentName: 'CategoryCard'
    },
    severity: ErrorSeverity.LOW,
    createdAt: new Date('2024-01-14T14:20:00Z'),
    resolved: false,
    occurrences: 12
  },
  {
    id: 5,
    errorMessage: 'ReferenceError: localStorage is not defined',
    screen: 'App',
    userAgent: 'Mozilla/5.0 (X11; Linux x86_64)',
    url: 'https://localhost:5173/',
    metadata: { 
      environment: 'SSR',
      nodeVersion: '18.17.0'
    },
    severity: ErrorSeverity.HIGH,
    createdAt: new Date('2024-01-13T11:10:00Z'),
    resolved: true,
    occurrences: 2
  }
])

// Dialog states
const showErrorDialog = ref(false)
const selectedError = ref(null)

// Filters and search
const filters = ref({
  global: { value: null, matchMode: FilterMatchMode.CONTAINS },
  severity: { value: null, matchMode: FilterMatchMode.EQUALS },
  screen: { value: null, matchMode: FilterMatchMode.CONTAINS },
  resolved: { value: null, matchMode: FilterMatchMode.EQUALS }
})

const searchTerm = ref('')
const selectedSeverities = ref([])
const selectedScreens = ref([])
const dateRange = ref([])
const showResolvedOnly = ref(false)

// Computed properties
const severityOptions = computed(() => 
  Object.values(ErrorSeverity).map(severity => ({
    label: severity,
    value: severity
  }))
)

const screenOptions = computed(() => {
  const screens = [...new Set(errors.value.map(error => error.screen).filter(Boolean))]
  return screens.map(screen => ({ label: screen, value: screen }))
})

const resolvedOptions = [
  { label: 'All', value: null },
  { label: 'Resolved', value: true },
  { label: 'Unresolved', value: false }
]

const filteredErrors = computed(() => {
  let filtered = errors.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(error => 
      error.errorMessage.toLowerCase().includes(search) ||
      error.screen?.toLowerCase().includes(search) ||
      error.url?.toLowerCase().includes(search)
    )
  }

  if (selectedSeverities.value.length > 0) {
    filtered = filtered.filter(error => 
      selectedSeverities.value.includes(error.severity)
    )
  }

  if (selectedScreens.value.length > 0) {
    filtered = filtered.filter(error => 
      selectedScreens.value.includes(error.screen)
    )
  }

  if (showResolvedOnly.value !== null) {
    filtered = filtered.filter(error => error.resolved === showResolvedOnly.value)
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(error => {
      const errorDate = new Date(error.createdAt)
      return errorDate >= startDate && errorDate <= endDate
    })
  }

  return filtered
})

const errorStats = computed(() => {
  const total = errors.value.length
  const resolved = errors.value.filter(e => e.resolved).length
  const critical = errors.value.filter(e => e.severity === ErrorSeverity.CRITICAL).length
  const high = errors.value.filter(e => e.severity === ErrorSeverity.HIGH).length
  
  return {
    total,
    resolved,
    unresolved: total - resolved,
    critical,
    high,
    resolutionRate: total > 0 ? Math.round((resolved / total) * 100) : 0
  }
})

// Methods
const getSeverityColor = (severity) => {
  const colors = {
    [ErrorSeverity.LOW]: 'info',
    [ErrorSeverity.MEDIUM]: 'warning',
    [ErrorSeverity.HIGH]: 'danger',
    [ErrorSeverity.CRITICAL]: 'danger'
  }
  return colors[severity] || 'info'
}

const getSeverityIcon = (severity) => {
  const icons = {
    [ErrorSeverity.LOW]: 'pi pi-info-circle',
    [ErrorSeverity.MEDIUM]: 'pi pi-exclamation-triangle',
    [ErrorSeverity.HIGH]: 'pi pi-times-circle',
    [ErrorSeverity.CRITICAL]: 'pi pi-ban'
  }
  return icons[severity] || 'pi pi-info-circle'
}

const viewErrorDetails = (error) => {
  selectedError.value = error
  showErrorDialog.value = true
}

const toggleErrorResolution = (error) => {
  const index = errors.value.findIndex(e => e.id === error.id)
  if (index !== -1) {
    errors.value[index].resolved = !errors.value[index].resolved
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const formatUserAgent = (userAgent) => {
  if (!userAgent) return 'Unknown'
  
  // Simple browser detection
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  
  return 'Other'
}

const clearFilters = () => {
  searchTerm.value = ''
  selectedSeverities.value = []
  selectedScreens.value = []
  dateRange.value = []
  showResolvedOnly.value = null
}

const exportErrors = () => {
  // Simple CSV export
  const csvContent = [
    ['ID', 'Error Message', 'Screen', 'Severity', 'URL', 'Created At', 'Resolved', 'Occurrences'].join(','),
    ...filteredErrors.value.map(error => [
      error.id,
      `"${error.errorMessage.replace(/"/g, '""')}"`,
      error.screen || '',
      error.severity,
      error.url || '',
      error.createdAt.toISOString(),
      error.resolved,
      error.occurrences
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `error-logs-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}
</script>

<template>
  <div class="error-tracking-view">
    <!-- Left Sidebar with Filters -->
    <div class="filters-sidebar">
      <div class="filters-header">
        <h3>
          <i class="pi pi-filter"></i>
          Filters
        </h3>
        <Button 
          icon="pi pi-filter-slash" 
          text
          rounded
          size="small"
          @click="clearFilters"
          :disabled="!searchTerm && selectedSeverities.length === 0 && selectedScreens.length === 0"
          v-tooltip="'Clear All Filters'"
        />
      </div>

      <div class="filters-content">
        <!-- Search -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-search"></i>
            Search
          </label>
          <InputText 
            v-model="searchTerm" 
            placeholder="Search errors..." 
            class="w-full"
          />
        </div>

        <!-- Severity Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-exclamation-triangle"></i>
            Severity
          </label>
          <MultiSelect
            v-model="selectedSeverities"
            :options="severityOptions"
            option-label="label"
            option-value="value"
            placeholder="Select severity"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Screen Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-window-maximize"></i>
            Screen
          </label>
          <MultiSelect
            v-model="selectedScreens"
            :options="screenOptions"
            option-label="label"
            option-value="value"
            placeholder="Select screens"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Resolution Status Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-check-circle"></i>
            Resolution Status
          </label>
          <Dropdown
            v-model="showResolvedOnly"
            :options="resolvedOptions"
            option-label="label"
            option-value="value"
            placeholder="Select status"
            class="w-full"
          />
        </div>

        <!-- Date Range Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-calendar"></i>
            Date Range
          </label>
          <Calendar
            v-model="dateRange"
            selection-mode="range"
            :manual-input="false"
            placeholder="Select date range"
            class="w-full"
          />
        </div>

        <!-- Statistics -->
        <div class="filter-stats">
          <h4>Statistics</h4>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">Total Errors</span>
              <span class="stat-value">{{ errorStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Resolved</span>
              <span class="stat-value success">{{ errorStats.resolved }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Critical</span>
              <span class="stat-value danger">{{ errorStats.critical }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">High Priority</span>
              <span class="stat-value warning">{{ errorStats.high }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Resolution Rate</span>
              <span class="stat-value">{{ errorStats.resolutionRate }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div>
            <h1>Error Tracking</h1>
            <p>Monitor and manage application errors and exceptions</p>
          </div>
          <div class="header-actions">
            <Button 
              label="Export" 
              icon="pi pi-download" 
              severity="secondary"
              @click="exportErrors"
            />
          </div>
        </div>
      </div>

      <!-- Errors Table -->
      <div class="table-container">
        <DataTable 
          :value="filteredErrors" 
          class="errors-table"
          :paginator="true" 
          :rows="20"
          :rows-per-page-options="[10, 20, 50]"
          stripedRows
          :rowHover="true"
          :filters="filters"
          :global-filter-fields="['errorMessage', 'screen', 'url']"
          sort-field="createdAt"
          :sort-order="-1"
          :scrollable="true"
          scroll-height="flex"
        >
          <Column field="severity" header="Severity" sortable style="width: 120px">
            <template #body="slotProps">
              <Tag 
                :value="slotProps.data.severity" 
                :severity="getSeverityColor(slotProps.data.severity)"
                :icon="getSeverityIcon(slotProps.data.severity)"
              />
            </template>
          </Column>
          
          <Column field="errorMessage" header="Error Message" sortable>
            <template #body="slotProps">
              <div class="error-message-cell">
                <span class="error-text" :title="slotProps.data.errorMessage">
                  {{ slotProps.data.errorMessage }}
                </span>
                <div class="error-meta">
                  <Chip 
                    v-if="slotProps.data.screen" 
                    :label="slotProps.data.screen" 
                    class="screen-chip"
                  />
                  <span class="occurrences">{{ slotProps.data.occurrences }}x</span>
                </div>
              </div>
            </template>
          </Column>
          
          <Column field="url" header="URL" sortable>
            <template #body="slotProps">
              <span class="url-text" :title="slotProps.data.url">
                {{ slotProps.data.url }}
              </span>
            </template>
          </Column>
          
          <Column field="userAgent" header="Browser" sortable style="width: 100px">
            <template #body="slotProps">
              <span class="browser-text">
                {{ formatUserAgent(slotProps.data.userAgent) }}
              </span>
            </template>
          </Column>
          
          <Column field="createdAt" header="Created" sortable style="width: 150px">
            <template #body="slotProps">
              <span class="created-date">{{ formatDate(slotProps.data.createdAt) }}</span>
            </template>
          </Column>
          
          <Column field="resolved" header="Status" sortable style="width: 100px">
            <template #body="slotProps">
              <Tag 
                :value="slotProps.data.resolved ? 'Resolved' : 'Open'" 
                :severity="slotProps.data.resolved ? 'success' : 'warning'"
                :icon="slotProps.data.resolved ? 'pi pi-check' : 'pi pi-clock'"
              />
            </template>
          </Column>
          
          <Column header="Actions" style="width: 120px">
            <template #body="slotProps">
              <div class="action-buttons">
                <Button 
                  icon="pi pi-eye" 
                  text 
                  rounded 
                  size="small" 
                  @click="viewErrorDetails(slotProps.data)"
                  v-tooltip="'View Details'"
                />
                <Button 
                  :icon="slotProps.data.resolved ? 'pi pi-times' : 'pi pi-check'" 
                  text 
                  rounded 
                  size="small" 
                  :severity="slotProps.data.resolved ? 'warning' : 'success'"
                  @click="toggleErrorResolution(slotProps.data)"
                  :v-tooltip="slotProps.data.resolved ? 'Mark as Unresolved' : 'Mark as Resolved'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Error Details Dialog -->
    <Dialog 
      v-model:visible="showErrorDialog" 
      header="Error Details"
      :modal="true" 
      class="error-dialog"
      :style="{ width: '800px' }"
    >
      <div v-if="selectedError" class="error-details">
        <div class="detail-section">
          <h4>Error Information</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>Severity:</label>
              <Tag 
                :value="selectedError.severity" 
                :severity="getSeverityColor(selectedError.severity)"
                :icon="getSeverityIcon(selectedError.severity)"
              />
            </div>
            <div class="detail-item">
              <label>Screen:</label>
              <span>{{ selectedError.screen || 'Unknown' }}</span>
            </div>
            <div class="detail-item">
              <label>Occurrences:</label>
              <span>{{ selectedError.occurrences }}</span>
            </div>
            <div class="detail-item">
              <label>Status:</label>
              <Tag 
                :value="selectedError.resolved ? 'Resolved' : 'Open'" 
                :severity="selectedError.resolved ? 'success' : 'warning'"
              />
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>Error Message</h4>
          <Textarea 
            :model-value="selectedError.errorMessage" 
            readonly 
            rows="3"
            class="w-full error-message-textarea"
          />
        </div>

        <div class="detail-section">
          <h4>URL</h4>
          <InputText 
            :model-value="selectedError.url" 
            readonly 
            class="w-full"
          />
        </div>

        <div class="detail-section">
          <h4>User Agent</h4>
          <Textarea 
            :model-value="selectedError.userAgent" 
            readonly 
            rows="2"
            class="w-full"
          />
        </div>

        <div class="detail-section" v-if="selectedError.metadata">
          <h4>Metadata</h4>
          <pre class="metadata-display">{{ JSON.stringify(selectedError.metadata, null, 2) }}</pre>
        </div>

        <div class="detail-section">
          <h4>Timestamp</h4>
          <span>{{ formatDate(selectedError.createdAt) }}</span>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            :label="selectedError?.resolved ? 'Mark as Unresolved' : 'Mark as Resolved'"
            :icon="selectedError?.resolved ? 'pi pi-times' : 'pi pi-check'"
            :severity="selectedError?.resolved ? 'warning' : 'success'"
            @click="toggleErrorResolution(selectedError); showErrorDialog = false"
          />
          <Button 
            label="Close" 
            severity="secondary" 
            @click="showErrorDialog = false"
          />
        </div>
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.error-tracking-view {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Filters Sidebar */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.stat-value.success {
  color: var(--p-green-600);
}

.stat-value.danger {
  color: var(--p-red-600);
}

.stat-value.warning {
  color: var(--p-yellow-600);
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-content p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* Table Container */
.table-container {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.errors-table {
  flex: 1;
  height: 100%;
}

.error-message-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.error-text {
  color: var(--p-text-color);
  font-size: 0.875rem;
  line-height: 1.4;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.error-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.screen-chip {
  font-size: 0.75rem;
  height: 1.5rem;
}

.occurrences {
  background: var(--p-surface-100);
  color: var(--p-text-muted-color);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

:global(.dark) .occurrences {
  background: var(--p-surface-700);
}

.url-text {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.browser-text {
  color: var(--p-text-color);
  font-size: 0.875rem;
  font-weight: 500;
}

.created-date {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.error-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-section h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
}

.error-message-textarea {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.metadata-display {
  background: var(--p-surface-50);
  border: 1px solid var(--p-surface-border);
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--p-text-color);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

:global(.dark) .metadata-display {
  background: var(--p-surface-800);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .error-tracking-view {
    flex-direction: column;
    height: auto;
  }
  
  .filters-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
  }
  
  .main-content {
    order: 1;
    height: 60vh;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .table-container {
    padding: 1rem;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>