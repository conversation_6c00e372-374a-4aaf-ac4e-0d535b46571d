import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { ProfileEntity } from './profile.entity';
import { AdminActivityEntity } from './admin-activity.entity';
import { TestimonialEntity } from './testimonial.entity';
import { UserRole, SubscriptionType } from '../enums/user.enum';
import { BusinessDetailsEntity } from './business-details.entity';
import { VerificationEntity } from './verification.entity';
import { JobEntity } from '../../jobs/entities/job.entity';
import { JobApplicationEntity } from '../../jobs/entities/job-application.entity';
import { JobFavoriteEntity } from '../../jobs/entities/job-favorite.entity';
import { FeedEntity } from '../../feeds/entities/feed.entity';
import { CompanyEntity } from '../../companies/entities/company.entity';

@Entity('users')
export class UserEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: true })
  email: string;

  @Column({ unique: true, nullable: true })
  phone: string;

  @Column({ select: false })
  password: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  middleName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  emailOtp: string;

  @Column({ nullable: true })
  emailOtpExpiry: Date;

  @Column({ default: false })
  isPhoneVerified: boolean;

  @Column({ default: false })
  isEmailVerified: boolean;

  @Column({ default: false })
  isProfileComplete: boolean;

  @Column({ default: false })
  isAadharVerified: boolean;

  @Column({ default: false })
  isBlocked: boolean;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.JOB_SEEKER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: SubscriptionType,
    default: SubscriptionType.DEFAULT,
  })
  subscriptionType: SubscriptionType;

  @Column({ nullable: true })
  profileId: string;

  @Column({ nullable: true })
  createdBy: string;

  @OneToOne(() => ProfileEntity, (profile) => profile.user, { cascade: true })
  @JoinColumn({ name: 'profileId' })
  profile: ProfileEntity;

  @OneToMany(() => AdminActivityEntity, (activity) => activity.admin)
  adminActivities: AdminActivityEntity[];

  @OneToMany(() => TestimonialEntity, (testimonial) => testimonial.user)
  testimonials: TestimonialEntity[];

  @OneToOne(() => BusinessDetailsEntity, (businessDetails) => businessDetails.user)
  businessDetails: BusinessDetailsEntity;

  @OneToOne(() => VerificationEntity, (verification) => verification.user)
  verification: VerificationEntity;

  @OneToMany(() => JobEntity, (job) => job.employer)
  jobs: JobEntity[];

  @OneToMany(() => JobApplicationEntity, (application) => application.applicant)
  jobApplications: JobApplicationEntity[];

  @OneToMany(() => JobFavoriteEntity, (favorite) => favorite.user)
  jobFavorites: JobFavoriteEntity[];

  @OneToMany(() => FeedEntity, (feed) => feed.author)
  feeds: FeedEntity[];

  @OneToOne(() => CompanyEntity, company => company.user)
  company: CompanyEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
