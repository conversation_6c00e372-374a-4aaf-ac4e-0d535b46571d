<script setup>
import { ref, computed, onMounted } from 'vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Tag from 'primevue/tag'
import ProgressBar from 'primevue/progressbar'

// Sample data that would come from the getDashboardData service
const dashboardData = ref({
  statistics: {
    totalJobs: 0,
    jobsByStatus: {}
  },
  recentActivity: {
    jobs: []
  }
})

// Additional admin-specific stats
const adminStats = ref({
  totalUsers: 1234,
  totalIndustries: 15,
  totalFeedback: 89,
  systemUptime: 99.8
})

// Sample job data (would come from the service)
const sampleJobs = [
  {
    id: '1',
    title: 'Senior Software Developer',
    industry: { name: 'Technology' },
    status: 'ACTIVE',
    salary: 75000,
    location: 'New York, NY',
    createdAt: new Date('2024-01-20T10:30:00Z'),
    applicationsCount: 15
  },
  {
    id: '2',
    title: 'Construction Worker',
    industry: { name: 'Construction' },
    status: 'PENDING',
    salary: 25,
    location: 'Los Angeles, CA',
    createdAt: new Date('2024-01-19T14:20:00Z'),
    applicationsCount: 8
  },
  {
    id: '3',
    title: 'Marketing Manager',
    industry: { name: 'Marketing' },
    status: 'CLOSED',
    salary: 65000,
    location: 'Chicago, IL',
    createdAt: new Date('2024-01-18T09:15:00Z'),
    applicationsCount: 23
  },
  {
    id: '4',
    title: 'Data Analyst',
    industry: { name: 'Technology' },
    status: 'ACTIVE',
    salary: 55000,
    location: 'Austin, TX',
    createdAt: new Date('2024-01-17T16:45:00Z'),
    applicationsCount: 12
  },
  {
    id: '5',
    title: 'Nurse Practitioner',
    industry: { name: 'Healthcare' },
    status: 'ACTIVE',
    salary: 80000,
    location: 'Boston, MA',
    createdAt: new Date('2024-01-16T11:30:00Z'),
    applicationsCount: 19
  }
]

// Simulate the service response
const initializeDashboard = () => {
  // Simulate job statistics
  const totalJobs = sampleJobs.length
  const jobsByStatus = sampleJobs.reduce((acc, job) => {
    acc[job.status] = (acc[job.status] || 0) + 1
    return acc
  }, {})

  dashboardData.value = {
    statistics: {
      totalJobs,
      jobsByStatus
    },
    recentActivity: {
      jobs: sampleJobs.slice(0, 5) // Recent 5 jobs
    }
  }
}

// Computed properties for dashboard metrics
const jobStats = computed(() => {
  const stats = dashboardData.value.statistics
  return {
    total: stats.totalJobs,
    active: stats.jobsByStatus.ACTIVE || 0,
    pending: stats.jobsByStatus.PENDING || 0,
    closed: stats.jobsByStatus.CLOSED || 0,
    draft: stats.jobsByStatus.DRAFT || 0
  }
})

// Recent activities (would come from various services)
const recentActivities = ref([
  {
    title: 'New job posted',
    description: 'Senior Software Developer • Technology',
    icon: 'pi pi-briefcase',
    time: '2 minutes ago',
    type: 'job'
  },
  {
    title: 'User registered',
    description: '<EMAIL> joined as Job Seeker',
    icon: 'pi pi-user-plus',
    time: '15 minutes ago',
    type: 'user'
  },
  {
    title: 'Feedback received',
    description: 'Bug report: Login form validation error',
    icon: 'pi pi-comments',
    time: '1 hour ago',
    type: 'feedback'
  },
  {
    title: 'Industry added',
    description: 'New industry: Renewable Energy',
    icon: 'pi pi-building',
    time: '2 hours ago',
    type: 'industry'
  },
  {
    title: 'System update',
    description: 'Version 2.1.0 deployed successfully',
    icon: 'pi pi-cog',
    time: '3 hours ago',
    type: 'system'
  }
])

// Methods
const getStatusColor = (status) => {
  const colors = {
    ACTIVE: 'success',
    PENDING: 'warning',
    CLOSED: 'danger',
    DRAFT: 'secondary'
  }
  return colors[status] || 'secondary'
}

const formatSalary = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getActivityIcon = (type) => {
  const icons = {
    job: 'pi pi-briefcase',
    user: 'pi pi-user-plus',
    feedback: 'pi pi-comments',
    industry: 'pi pi-building',
    system: 'pi pi-cog'
  }
  return icons[type] || 'pi pi-info-circle'
}

const getActivityColor = (type) => {
  const colors = {
    job: 'success',
    user: 'info',
    feedback: 'warning',
    industry: 'secondary',
    system: 'help'
  }
  return colors[type] || 'secondary'
}

// Initialize dashboard on mount
onMounted(() => {
  initializeDashboard()
})
</script>

<template>
  <div class="dashboard-view">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1>Admin Dashboard</h1>
          <p>Welcome back! Here's what's happening with your platform.</p>
        </div>
        <div class="header-actions">
          <Button label="Refresh Data" icon="pi pi-refresh" severity="secondary" @click="initializeDashboard" />
          <Button label="Export Report" icon="pi pi-download" />
        </div>
      </div>
    </div>
    
    <!-- Main Statistics Grid -->
    <div class="stats-grid">
      <!-- Job Statistics -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon jobs">
              <i class="pi pi-briefcase"></i>
            </div>
            <div class="stat-info">
              <h3>{{ jobStats.total }}</h3>
              <p>Total Jobs</p>
              <div class="stat-breakdown">
                <span class="breakdown-item success">{{ jobStats.active }} Active</span>
                <span class="breakdown-item warning">{{ jobStats.pending }} Pending</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- User Statistics -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon users">
              <i class="pi pi-users"></i>
            </div>
            <div class="stat-info">
              <h3>{{ adminStats.totalUsers.toLocaleString() }}</h3>
              <p>Total Users</p>
              <div class="progress-container">
                <span class="progress-text">+12% this month</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Industries -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon industries">
              <i class="pi pi-building"></i>
            </div>
            <div class="stat-info">
              <h3>{{ adminStats.totalIndustries }}</h3>
              <p>Industries</p>
              <div class="progress-container">
                <span class="progress-text">2 new this week</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- System Health -->
      <Card class="stat-card">
        <template #content>
          <div class="stat-content">
            <div class="stat-icon uptime">
              <i class="pi pi-chart-line"></i>
            </div>
            <div class="stat-info">
              <h3>{{ adminStats.systemUptime }}%</h3>
              <p>System Uptime</p>
              <div class="progress-container">
                <ProgressBar 
                  :value="adminStats.systemUptime" 
                  :show-value="false"
                  class="uptime-progress"
                />
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Dashboard Content Grid -->
    <div class="dashboard-grid">
      <!-- Job Status Summary -->
      <Card class="summary-card">
        <template #title>
          <div class="card-header">
            <h3>Job Status Summary</h3>
            <Button icon="pi pi-refresh" text rounded size="small" @click="initializeDashboard" />
          </div>
        </template>
        <template #content>
          <div class="status-summary">
            <div class="status-item">
              <div class="status-icon active">
                <i class="pi pi-check-circle"></i>
              </div>
              <div class="status-info">
                <h4>{{ jobStats.active }}</h4>
                <p>Active Jobs</p>
                <span class="status-percentage">{{ Math.round((jobStats.active / jobStats.total) * 100) }}%</span>
              </div>
            </div>
            
            <div class="status-item">
              <div class="status-icon pending">
                <i class="pi pi-clock"></i>
              </div>
              <div class="status-info">
                <h4>{{ jobStats.pending }}</h4>
                <p>Pending Jobs</p>
                <span class="status-percentage">{{ Math.round((jobStats.pending / jobStats.total) * 100) }}%</span>
              </div>
            </div>
            
            <div class="status-item">
              <div class="status-icon closed">
                <i class="pi pi-times-circle"></i>
              </div>
              <div class="status-info">
                <h4>{{ jobStats.closed }}</h4>
                <p>Closed Jobs</p>
                <span class="status-percentage">{{ Math.round((jobStats.closed / jobStats.total) * 100) }}%</span>
              </div>
            </div>
            
            <div class="status-item">
              <div class="status-icon draft">
                <i class="pi pi-file"></i>
              </div>
              <div class="status-info">
                <h4>{{ jobStats.draft }}</h4>
                <p>Draft Jobs</p>
                <span class="status-percentage">{{ Math.round((jobStats.draft / jobStats.total) * 100) }}%</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Recent Jobs -->
      <Card class="recent-jobs-card">
        <template #title>
          <div class="card-header">
            <h3>Recent Jobs</h3>
            <Button label="View All" text size="small" @click="$router.push('/jdAdmin/jobs')" />
          </div>
        </template>
        <template #content>
          <DataTable 
            :value="dashboardData.recentActivity.jobs" 
            class="recent-jobs-table"
            :paginator="false"
            :rows="5"
          >
            <Column field="title" header="Job Title">
              <template #body="slotProps">
                <div class="job-title-cell">
                  <span class="job-title">{{ slotProps.data.title }}</span>
                  <span class="job-industry">{{ slotProps.data.industry?.name }}</span>
                </div>
              </template>
            </Column>
            
            <Column field="status" header="Status">
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.status" 
                  :severity="getStatusColor(slotProps.data.status)"
                />
              </template>
            </Column>
            
            <Column field="salary" header="Salary">
              <template #body="slotProps">
                <span class="salary-text">{{ formatSalary(slotProps.data.salary) }}</span>
              </template>
            </Column>
            
            <Column field="applicationsCount" header="Applications">
              <template #body="slotProps">
                <span class="applications-count">{{ slotProps.data.applicationsCount || 0 }}</span>
              </template>
            </Column>
            
            <Column field="createdAt" header="Posted">
              <template #body="slotProps">
                <span class="posted-date">{{ formatDate(slotProps.data.createdAt) }}</span>
              </template>
            </Column>
          </DataTable>
        </template>
      </Card>
    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="bottom-grid">
      <!-- Recent Activity -->
      <Card class="activity-card">
        <template #title>
          <div class="card-header">
            <h3>Recent Activity</h3>
            <Button icon="pi pi-external-link" text rounded size="small" />
          </div>
        </template>
        <template #content>
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.title" class="activity-item">
              <div :class="['activity-icon', getActivityColor(activity.type)]">
                <i :class="getActivityIcon(activity.type)"></i>
              </div>
              <div class="activity-content">
                <p><strong>{{ activity.title }}</strong></p>
                <span class="activity-description">{{ activity.description }}</span>
                <span class="activity-time">{{ activity.time }}</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Quick Actions -->
      <Card class="quick-actions-card">
        <template #title>
          <div class="card-header">
            <h3>Quick Actions</h3>
          </div>
        </template>
        <template #content>
          <div class="quick-actions">
            <Button 
              label="Add User" 
              icon="pi pi-user-plus" 
              class="action-btn" 
              @click="$router.push('/jdAdmin/users')"
            />
            <Button 
              label="Create Job" 
              icon="pi pi-briefcase" 
              class="action-btn" 
              severity="success"
              @click="$router.push('/jdAdmin/jobs')"
            />
            <Button 
              label="Add Industry" 
              icon="pi pi-building" 
              class="action-btn" 
              severity="info"
              @click="$router.push('/jdAdmin/industries')"
            />
            <Button 
              label="View Feedback" 
              icon="pi pi-comments" 
              class="action-btn" 
              severity="warning"
              @click="$router.push('/jdAdmin/feedback')"
            />
            <Button 
              label="System Settings" 
              icon="pi pi-cog" 
              class="action-btn" 
              severity="secondary"
              @click="$router.push('/jdAdmin/settings')"
            />
            <Button 
              label="Error Tracking" 
              icon="pi pi-exclamation-triangle" 
              class="action-btn" 
              severity="danger"
              @click="$router.push('/jdAdmin/errors')"
            />
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<style scoped>
.dashboard-view {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.page-header {
  margin-bottom: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-content p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.stat-icon.jobs { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-icon.users { background: linear-gradient(135deg, #f093fb, #f5576c); }
.stat-icon.industries { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.stat-icon.uptime { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.stat-info {
  flex: 1;
}

.stat-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.stat-info p {
  margin: 0 0 0.5rem 0;
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.stat-breakdown {
  display: flex;
  gap: 0.75rem;
}

.breakdown-item {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

.breakdown-item.success {
  background: var(--p-green-100);
  color: var(--p-green-700);
}

.breakdown-item.warning {
  background: var(--p-yellow-100);
  color: var(--p-yellow-700);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.progress-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
}

.uptime-progress {
  flex: 1;
  height: 6px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.bottom-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.summary-card,
.recent-jobs-card,
.activity-card,
.quick-actions-card {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.status-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--p-surface-50);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.status-item:hover {
  background: var(--p-surface-100);
}

:global(.dark) .status-item {
  background: var(--p-surface-800);
}

:global(.dark) .status-item:hover {
  background: var(--p-surface-700);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.status-icon.active { background: var(--p-green-500); }
.status-icon.pending { background: var(--p-yellow-500); }
.status-icon.closed { background: var(--p-red-500); }
.status-icon.draft { background: var(--p-surface-500); }

.status-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.status-info p {
  margin: 0 0 0.25rem 0;
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.status-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
}

.recent-jobs-table {
  font-size: 0.875rem;
}

.job-title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.job-title {
  font-weight: 600;
  color: var(--p-text-color);
}

.job-industry {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.salary-text,
.applications-count,
.posted-date {
  font-size: 0.875rem;
  color: var(--p-text-color);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--p-surface-50);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.activity-item:hover {
  background: var(--p-surface-100);
}

:global(.dark) .activity-item {
  background: var(--p-surface-800);
}

:global(.dark) .activity-item:hover {
  background: var(--p-surface-700);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: white;
  flex-shrink: 0;
}

.activity-icon.success { background: var(--p-green-500); }
.activity-icon.info { background: var(--p-blue-500); }
.activity-icon.warning { background: var(--p-yellow-500); }
.activity-icon.secondary { background: var(--p-surface-500); }
.activity-icon.help { background: var(--p-purple-500); }

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.activity-description {
  display: block;
  color: var(--p-text-muted-color);
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.activity-time {
  color: var(--p-text-muted-color);
  font-size: 0.75rem;
  font-style: italic;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-btn {
  justify-content: flex-start;
  text-align: left;
}

@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .bottom-grid {
    grid-template-columns: 1fr;
  }
  
  .status-summary {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .status-summary {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}
</style>