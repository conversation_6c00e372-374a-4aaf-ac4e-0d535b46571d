import httpClient from '../httpClient'
import { ENDPOINTS } from '../endpoints'

export const companiesService = {
  // Get all companies
  async getAll(params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.COMPANIES.LIST, { params })
      return {
        success: true,
        data: response.companies || response.data || [],
        pagination: response.pagination,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch companies')
    }
  },

  // Get company by ID
  async getById(id) {
    try {
      const response = await httpClient.get(ENDPOINTS.COMPANIES.DETAILS(id))
      return {
        success: true,
        data: response.company || response.data,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Company not found')
    }
  },

  // Get jobs by company
  async getJobs(id, params = {}) {
    try {
      const response = await httpClient.get(ENDPOINTS.COMPANIES.JOBS(id), { params })
      return {
        success: true,
        data: response.jobs || response.data || [],
        pagination: response.pagination,
        message: response.message
      }
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch company jobs')
    }
  }
}