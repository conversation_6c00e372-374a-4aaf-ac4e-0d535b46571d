<template>
  <div class="app-layout">
    <!-- Side Navigation -->
    <SideNavigation />
    
    <!-- Main Content Area -->
    <div class="main-content" :class="{ 'collapsed': isNavCollapsed }">
      <!-- Mobile Header (only visible on mobile) -->
      <div class="mobile-header">
        <Button 
          @click="toggleMobileNav"
          icon="pi pi-bars"
          text
          class="mobile-nav-toggle"
        />
        <div class="mobile-logo">
          <i class="pi pi-briefcase"></i>
          <span>Job Dalal</span>
        </div>
      </div>
      
      <!-- Page Content -->
      <div class="page-content">
        <slot />
      </div>
    </div>
    
    <!-- Mobile Navigation Overlay -->
    <div 
      v-if="isMobileNavOpen" 
      class="mobile-nav-overlay"
      @click="closeMobileNav"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Button from 'primevue/button'
import SideNavigation from './SideNavigation.vue'

const isNavCollapsed = ref(false)
const isMobileNavOpen = ref(false)

const toggleMobileNav = () => {
  isMobileNavOpen.value = !isMobileNavOpen.value
  
  // Add/remove class to side navigation for mobile
  const sideNav = document.querySelector('.side-navigation')
  if (sideNav) {
    sideNav.classList.toggle('mobile-open', isMobileNavOpen.value)
  }
}

const closeMobileNav = () => {
  isMobileNavOpen.value = false
  
  const sideNav = document.querySelector('.side-navigation')
  if (sideNav) {
    sideNav.classList.remove('mobile-open')
  }
}

const handleNavCollapse = () => {
  const savedCollapsed = localStorage.getItem('sideNavCollapsed')
  isNavCollapsed.value = savedCollapsed === 'true'
}

const handleStorageChange = (e) => {
  if (e.key === 'sideNavCollapsed') {
    isNavCollapsed.value = e.newValue === 'true'
  }
}

onMounted(() => {
  handleNavCollapse()
  window.addEventListener('storage', handleStorageChange)
  
  // Listen for custom event from SideNavigation component
  const checkCollapsed = () => {
    const savedCollapsed = localStorage.getItem('sideNavCollapsed')
    isNavCollapsed.value = savedCollapsed === 'true'
  }
  
  // Check periodically for changes
  const interval = setInterval(checkCollapsed, 100)
  
  onUnmounted(() => {
    clearInterval(interval)
    window.removeEventListener('storage', handleStorageChange)
  })
})
</script>

<style scoped>
.app-layout {
  display: flex;
  min-height: 100vh;
  background: var(--surface-0);
}

.main-content {
  flex: 1;
  margin-left: 280px;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content.collapsed {
  margin-left: 80px;
}

.mobile-header {
  display: none;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  position: sticky;
  top: 0;
  z-index: 999;
}

.mobile-nav-toggle {
  color: var(--text-color) !important;
}

.mobile-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-color);
}

.mobile-logo i {
  color: var(--primary-color);
}

.page-content {
  flex: 1;
  background: linear-gradient(135deg, var(--surface-0) 0%, var(--surface-50) 100%);
}

.mobile-nav-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  
  .main-content.collapsed {
    margin-left: 0;
  }
  
  .mobile-header {
    display: flex;
  }
  
  .mobile-nav-overlay {
    display: block;
  }
}
</style>