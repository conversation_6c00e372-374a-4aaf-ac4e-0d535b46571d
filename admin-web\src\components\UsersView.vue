<script setup>
import { ref, reactive, computed } from 'vue'
import Card from 'primevue/card'
import But<PERSON> from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Dialog from 'primevue/dialog'
import Dropdown from 'primevue/dropdown'
import Checkbox from 'primevue/checkbox'
import Tag from 'primevue/tag'
import Calendar from 'primevue/calendar'
import MultiSelect from 'primevue/multiselect'
import ProgressBar from 'primevue/progressbar'
import ConfirmDialog from 'primevue/confirmdialog'
import Sidebar from 'primevue/sidebar'
import { useConfirm } from 'primevue/useconfirm'

const confirm = useConfirm()

// Enums based on the User Entity
const UserRole = {
  JOB_SEEKER: 'JOB_SEEKER',
  EMPLOYER: 'EMPLOYER',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
}

const SubscriptionType = {
  DEFAULT: 'DEFAULT',
  PREMIUM: 'PREMIUM',
  ENTERPRISE: 'ENTERPRISE'
}

// Sample users data based on the entity
const users = ref([
  {
    id: '1',
    email: '<EMAIL>',
    phone: '+1234567890',
    firstName: 'John',
    middleName: 'Michael',
    lastName: 'Doe',
    isPhoneVerified: true,
    isEmailVerified: true,
    isProfileComplete: true,
    isAadharVerified: false,
    isBlocked: false,
    role: UserRole.JOB_SEEKER,
    subscriptionType: SubscriptionType.PREMIUM,
    profileId: 'profile-1',
    createdBy: 'admin',
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-20T14:45:00Z'),
    // Additional computed fields
    fullName: 'John Michael Doe',
    verificationScore: 75
  },
  {
    id: '2',
    email: '<EMAIL>',
    phone: '+1987654321',
    firstName: 'Jane',
    middleName: null,
    lastName: 'Smith',
    isPhoneVerified: true,
    isEmailVerified: true,
    isProfileComplete: true,
    isAadharVerified: true,
    isBlocked: false,
    role: UserRole.EMPLOYER,
    subscriptionType: SubscriptionType.ENTERPRISE,
    profileId: 'profile-2',
    createdBy: 'system',
    createdAt: new Date('2024-01-10T09:15:00Z'),
    updatedAt: new Date('2024-01-18T16:20:00Z'),
    fullName: 'Jane Smith',
    verificationScore: 100
  },
  {
    id: '3',
    email: '<EMAIL>',
    phone: '+1555000123',
    firstName: 'Admin',
    middleName: null,
    lastName: 'User',
    isPhoneVerified: true,
    isEmailVerified: true,
    isProfileComplete: true,
    isAadharVerified: true,
    isBlocked: false,
    role: UserRole.ADMIN,
    subscriptionType: SubscriptionType.DEFAULT,
    profileId: 'profile-3',
    createdBy: 'super_admin',
    createdAt: new Date('2024-01-05T08:00:00Z'),
    updatedAt: new Date('2024-01-22T11:30:00Z'),
    fullName: 'Admin User',
    verificationScore: 100
  },
  {
    id: '4',
    email: '<EMAIL>',
    phone: '+1444555666',
    firstName: 'Bob',
    middleName: 'William',
    lastName: 'Johnson',
    isPhoneVerified: false,
    isEmailVerified: true,
    isProfileComplete: false,
    isAadharVerified: false,
    isBlocked: false,
    role: UserRole.JOB_SEEKER,
    subscriptionType: SubscriptionType.DEFAULT,
    profileId: null,
    createdBy: null,
    createdAt: new Date('2024-01-25T14:20:00Z'),
    updatedAt: new Date('2024-01-25T14:20:00Z'),
    fullName: 'Bob William Johnson',
    verificationScore: 25
  },
  {
    id: '5',
    email: '<EMAIL>',
    phone: '+1777888999',
    firstName: 'Blocked',
    middleName: null,
    lastName: 'User',
    isPhoneVerified: true,
    isEmailVerified: false,
    isProfileComplete: false,
    isAadharVerified: false,
    isBlocked: true,
    role: UserRole.JOB_SEEKER,
    subscriptionType: SubscriptionType.DEFAULT,
    profileId: 'profile-5',
    createdBy: 'admin',
    createdAt: new Date('2024-01-12T16:45:00Z'),
    updatedAt: new Date('2024-01-20T09:15:00Z'),
    fullName: 'Blocked User',
    verificationScore: 25
  }
])

// Dialog states
const showUserDialog = ref(false)
const isEditMode = ref(false)
const selectedUser = ref(null)

// Filter states
const showFilters = ref(false)
const filtersCollapsed = ref(false)

// Form data
const userForm = reactive({
  email: '',
  phone: '',
  password: '',
  firstName: '',
  middleName: '',
  lastName: '',
  isPhoneVerified: false,
  isEmailVerified: false,
  isProfileComplete: false,
  isAadharVerified: false,
  isBlocked: false,
  role: UserRole.JOB_SEEKER,
  subscriptionType: SubscriptionType.DEFAULT,
  createdBy: ''
})

// Search and filters
const searchTerm = ref('')
const selectedRoleFilter = ref([])
const selectedSubscriptionFilter = ref([])
const selectedStatusFilter = ref([])
const dateRange = ref([])
const verificationFilter = ref([])

// Options for dropdowns
const roleOptions = Object.values(UserRole).map(role => ({
  label: role.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: role
}))

const subscriptionOptions = Object.values(SubscriptionType).map(type => ({
  label: type.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const statusOptions = [
  { label: 'Active', value: 'active' },
  { label: 'Blocked', value: 'blocked' }
]

const verificationOptions = [
  { label: 'Email Verified', value: 'email' },
  { label: 'Phone Verified', value: 'phone' },
  { label: 'Aadhar Verified', value: 'aadhar' },
  { label: 'Profile Complete', value: 'profile' }
]

// Computed properties
const filteredUsers = computed(() => {
  let filtered = users.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.fullName.toLowerCase().includes(search) ||
      user.email.toLowerCase().includes(search) ||
      user.phone?.toLowerCase().includes(search)
    )
  }

  if (selectedRoleFilter.value.length > 0) {
    filtered = filtered.filter(user => 
      selectedRoleFilter.value.includes(user.role)
    )
  }

  if (selectedSubscriptionFilter.value.length > 0) {
    filtered = filtered.filter(user => 
      selectedSubscriptionFilter.value.includes(user.subscriptionType)
    )
  }

  if (selectedStatusFilter.value.length > 0) {
    filtered = filtered.filter(user => {
      const isBlocked = user.isBlocked
      return selectedStatusFilter.value.includes(isBlocked ? 'blocked' : 'active')
    })
  }

  if (verificationFilter.value.length > 0) {
    filtered = filtered.filter(user => {
      return verificationFilter.value.every(filter => {
        switch (filter) {
          case 'email': return user.isEmailVerified
          case 'phone': return user.isPhoneVerified
          case 'aadhar': return user.isAadharVerified
          case 'profile': return user.isProfileComplete
          default: return true
        }
      })
    })
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(user => {
      const userDate = new Date(user.createdAt)
      return userDate >= startDate && userDate <= endDate
    })
  }

  return filtered
})

const userStats = computed(() => {
  const total = users.value.length
  const active = users.value.filter(u => !u.isBlocked).length
  const blocked = users.value.filter(u => u.isBlocked).length
  const verified = users.value.filter(u => u.isEmailVerified && u.isPhoneVerified).length
  const jobSeekers = users.value.filter(u => u.role === UserRole.JOB_SEEKER).length
  const employers = users.value.filter(u => u.role === UserRole.EMPLOYER).length
  
  return {
    total,
    active,
    blocked,
    verified,
    jobSeekers,
    employers,
    verificationRate: total > 0 ? Math.round((verified / total) * 100) : 0
  }
})

// Methods
const getRoleColor = (role) => {
  const colors = {
    [UserRole.JOB_SEEKER]: 'info',
    [UserRole.EMPLOYER]: 'success',
    [UserRole.ADMIN]: 'warning',
    [UserRole.SUPER_ADMIN]: 'danger'
  }
  return colors[role] || 'info'
}

const getSubscriptionColor = (subscription) => {
  const colors = {
    [SubscriptionType.DEFAULT]: 'secondary',
    [SubscriptionType.PREMIUM]: 'info',
    [SubscriptionType.ENTERPRISE]: 'success'
  }
  return colors[subscription] || 'secondary'
}

const getVerificationColor = (score) => {
  if (score >= 75) return 'success'
  if (score >= 50) return 'warning'
  return 'danger'
}

const openUserDialog = (user = null) => {
  isEditMode.value = !!user
  if (user) {
    // Populate form with user data
    Object.keys(userForm).forEach(key => {
      if (user[key] !== undefined) {
        userForm[key] = user[key]
      }
    })
    userForm.password = '' // Don't populate password for security
    selectedUser.value = user
  } else {
    resetUserForm()
  }
  showUserDialog.value = true
}

const resetUserForm = () => {
  Object.assign(userForm, {
    email: '',
    phone: '',
    password: '',
    firstName: '',
    middleName: '',
    lastName: '',
    isPhoneVerified: false,
    isEmailVerified: false,
    isProfileComplete: false,
    isAadharVerified: false,
    isBlocked: false,
    role: UserRole.JOB_SEEKER,
    subscriptionType: SubscriptionType.DEFAULT,
    createdBy: ''
  })
  selectedUser.value = null
}

const saveUser = () => {
  if (!userForm.email.trim() || !userForm.firstName.trim() || !userForm.lastName.trim()) return
  if (!isEditMode.value && !userForm.password.trim()) return

  const fullName = `${userForm.firstName} ${userForm.middleName || ''} ${userForm.lastName}`.replace(/\s+/g, ' ').trim()
  
  // Calculate verification score
  let verificationScore = 0
  if (userForm.isEmailVerified) verificationScore += 25
  if (userForm.isPhoneVerified) verificationScore += 25
  if (userForm.isAadharVerified) verificationScore += 25
  if (userForm.isProfileComplete) verificationScore += 25

  if (isEditMode.value && selectedUser.value) {
    // Update existing user
    const index = users.value.findIndex(u => u.id === selectedUser.value.id)
    if (index !== -1) {
      users.value[index] = {
        ...users.value[index],
        ...userForm,
        fullName,
        verificationScore,
        updatedAt: new Date()
      }
    }
  } else {
    // Add new user
    const newUser = {
      ...userForm,
      id: Date.now().toString(),
      fullName,
      verificationScore,
      profileId: userForm.isProfileComplete ? `profile-${Date.now()}` : null,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    users.value.push(newUser)
  }

  showUserDialog.value = false
  resetUserForm()
}

const deleteUser = (user) => {
  confirm.require({
    message: `Are you sure you want to delete user "${user.fullName}"?`,
    header: 'Delete User',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const index = users.value.findIndex(u => u.id === user.id)
      if (index !== -1) {
        users.value.splice(index, 1)
      }
    }
  })
}

const toggleUserStatus = (user) => {
  const index = users.value.findIndex(u => u.id === user.id)
  if (index !== -1) {
    users.value[index].isBlocked = !users.value[index].isBlocked
    users.value[index].updatedAt = new Date()
  }
}

const toggleVerification = (user, type) => {
  const index = users.value.findIndex(u => u.id === user.id)
  if (index !== -1) {
    switch (type) {
      case 'email':
        users.value[index].isEmailVerified = !users.value[index].isEmailVerified
        break
      case 'phone':
        users.value[index].isPhoneVerified = !users.value[index].isPhoneVerified
        break
      case 'aadhar':
        users.value[index].isAadharVerified = !users.value[index].isAadharVerified
        break
      case 'profile':
        users.value[index].isProfileComplete = !users.value[index].isProfileComplete
        break
    }
    
    // Recalculate verification score
    let verificationScore = 0
    if (users.value[index].isEmailVerified) verificationScore += 25
    if (users.value[index].isPhoneVerified) verificationScore += 25
    if (users.value[index].isAadharVerified) verificationScore += 25
    if (users.value[index].isProfileComplete) verificationScore += 25
    
    users.value[index].verificationScore = verificationScore
    users.value[index].updatedAt = new Date()
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const clearFilters = () => {
  searchTerm.value = ''
  selectedRoleFilter.value = []
  selectedSubscriptionFilter.value = []
  selectedStatusFilter.value = []
  verificationFilter.value = []
  dateRange.value = []
}

const exportUsers = () => {
  // Simple CSV export
  const csvContent = [
    ['ID', 'Full Name', 'Email', 'Phone', 'Role', 'Subscription', 'Status', 'Verification Score', 'Created At'].join(','),
    ...filteredUsers.value.map(user => [
      user.id,
      `"${user.fullName.replace(/"/g, '""')}"`,
      user.email,
      user.phone || '',
      user.role,
      user.subscriptionType,
      user.isBlocked ? 'Blocked' : 'Active',
      user.verificationScore,
      user.createdAt.toISOString()
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `users-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const toggleFiltersCollapse = () => {
  filtersCollapsed.value = !filtersCollapsed.value
}
</script>

<template>
  <div class="users-view">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <Button
            icon="pi pi-filter"
            text
            rounded
            @click="toggleFilters"
            class="filter-toggle mobile-only"
            aria-label="Toggle filters"
            v-tooltip="'Toggle Filters'"
          />
          <div class="header-title">
            <h1>User Management</h1>
            <p>Manage user accounts, roles, and verification status</p>
          </div>
        </div>
        <div class="header-actions">
          <Button 
            label="Export" 
            icon="pi pi-download" 
            severity="secondary"
            @click="exportUsers"
          />
          <Button 
            label="Add User" 
            icon="pi pi-plus" 
            @click="openUserDialog()"
          />
        </div>
      </div>
    </div>

    <div class="content-layout">
      <!-- Left Sidebar with Filters (Desktop) -->
      <div :class="['filters-sidebar', 'desktop-only', { collapsed: filtersCollapsed }]">
        <div class="filters-header">
          <h3 v-if="!filtersCollapsed">
            <i class="pi pi-filter"></i>
            Filters
          </h3>
          <Button 
            :icon="filtersCollapsed ? 'pi pi-angle-right' : 'pi pi-angle-left'"
            text
            rounded
            size="small"
            @click="toggleFiltersCollapse"
            v-tooltip="filtersCollapsed ? 'Expand Filters' : 'Collapse Filters'"
            class="collapse-btn"
          />
        </div>

        <div v-if="!filtersCollapsed" class="filters-content">
          <!-- Search -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-search"></i>
              Search
            </label>
            <InputText 
              v-model="searchTerm" 
              placeholder="Search users..." 
              class="w-full"
            />
          </div>

          <!-- Role Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-users"></i>
              Role
            </label>
            <MultiSelect
              v-model="selectedRoleFilter"
              :options="roleOptions"
              option-label="label"
              option-value="value"
              placeholder="Select roles"
              class="w-full"
              :max-selected-labels="2"
            />
          </div>

          <!-- Subscription Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-star"></i>
              Subscription
            </label>
            <MultiSelect
              v-model="selectedSubscriptionFilter"
              :options="subscriptionOptions"
              option-label="label"
              option-value="value"
              placeholder="Select subscriptions"
              class="w-full"
              :max-selected-labels="2"
            />
          </div>

          <!-- Status Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-check-circle"></i>
              Status
            </label>
            <MultiSelect
              v-model="selectedStatusFilter"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Select status"
              class="w-full"
              :max-selected-labels="2"
            />
          </div>

          <!-- Verification Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-verified"></i>
              Verification
            </label>
            <MultiSelect
              v-model="verificationFilter"
              :options="verificationOptions"
              option-label="label"
              option-value="value"
              placeholder="Select verification"
              class="w-full"
              :max-selected-labels="3"
            />
          </div>

          <!-- Date Range Filter -->
          <div class="filter-group">
            <label class="filter-label">
              <i class="pi pi-calendar"></i>
              Date Range
            </label>
            <Calendar
              v-model="dateRange"
              selection-mode="range"
              :manual-input="false"
              placeholder="Select date range"
              class="w-full"
            />
          </div>

          <!-- Statistics -->
          <div class="filter-stats">
            <h4>Statistics</h4>
            <div class="stats-list">
              <div class="stat-item">
                <span class="stat-label">Total Users</span>
                <span class="stat-value">{{ userStats.total }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Active</span>
                <span class="stat-value success">{{ userStats.active }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Verified</span>
                <span class="stat-value info">{{ userStats.verified }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Job Seekers</span>
                <span class="stat-value">{{ userStats.jobSeekers }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Employers</span>
                <span class="stat-value">{{ userStats.employers }}</span>
              </div>
            </div>
          </div>

          <!-- Clear Filters -->
          <Button 
            label="Clear All Filters"
            icon="pi pi-filter-slash" 
            severity="secondary"
            @click="clearFilters"
            :disabled="!searchTerm && selectedRoleFilter.length === 0 && selectedSubscriptionFilter.length === 0"
            class="w-full"
          />
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="main-content">
        <!-- Users Table -->
        <div class="table-container">
          <DataTable 
            :value="filteredUsers" 
            class="users-table"
            :paginator="true" 
            :rows="20"
            :rows-per-page-options="[10, 20, 50]"
            stripedRows
            :rowHover="true"
            sort-field="createdAt"
            :sort-order="-1"
            :scrollable="true"
            scroll-height="flex"
          >
            <Column field="fullName" header="User" sortable>
              <template #body="slotProps">
                <div class="user-cell">
                  <div class="user-avatar">
                    <i class="pi pi-user"></i>
                  </div>
                  <div class="user-info">
                    <span class="user-name">{{ slotProps.data.fullName }}</span>
                    <span class="user-email">{{ slotProps.data.email }}</span>
                    <span class="user-phone" v-if="slotProps.data.phone">{{ slotProps.data.phone }}</span>
                  </div>
                </div>
              </template>
            </Column>
            
            <Column field="role" header="Role" sortable style="width: 120px">
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.role.replace(/_/g, ' ')" 
                  :severity="getRoleColor(slotProps.data.role)"
                />
              </template>
            </Column>
            
            <Column field="subscriptionType" header="Subscription" sortable style="width: 130px">
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.subscriptionType" 
                  :severity="getSubscriptionColor(slotProps.data.subscriptionType)"
                />
              </template>
            </Column>
            
            <Column header="Verification" style="width: 150px">
              <template #body="slotProps">
                <div class="verification-cell">
                  <div class="verification-score">
                    <ProgressBar 
                      :value="slotProps.data.verificationScore" 
                      :show-value="false"
                      :class="['score-bar', getVerificationColor(slotProps.data.verificationScore)]"
                    />
                    <span class="score-text">{{ slotProps.data.verificationScore }}%</span>
                  </div>
                  <div class="verification-badges">
                    <i 
                      v-if="slotProps.data.isEmailVerified" 
                      class="pi pi-envelope verification-badge verified" 
                      title="Email Verified"
                    ></i>
                    <i 
                      v-if="slotProps.data.isPhoneVerified" 
                      class="pi pi-phone verification-badge verified" 
                      title="Phone Verified"
                    ></i>
                    <i 
                      v-if="slotProps.data.isAadharVerified" 
                      class="pi pi-id-card verification-badge verified" 
                      title="Aadhar Verified"
                    ></i>
                    <i 
                      v-if="slotProps.data.isProfileComplete" 
                      class="pi pi-user verification-badge verified" 
                      title="Profile Complete"
                    ></i>
                  </div>
                </div>
              </template>
            </Column>
            
            <Column field="createdAt" header="Created" sortable style="width: 150px">
              <template #body="slotProps">
                <span class="created-date">{{ formatDate(slotProps.data.createdAt) }}</span>
              </template>
            </Column>
            
            <Column field="isBlocked" header="Status" sortable style="width: 100px">
              <template #body="slotProps">
                <Tag 
                  :value="slotProps.data.isBlocked ? 'Blocked' : 'Active'" 
                  :severity="slotProps.data.isBlocked ? 'danger' : 'success'"
                />
              </template>
            </Column>
            
            <Column header="Actions" style="width: 150px">
              <template #body="slotProps">
                <div class="action-buttons">
                  <Button 
                    icon="pi pi-pencil" 
                    text 
                    rounded 
                    size="small" 
                    @click="openUserDialog(slotProps.data)"
                    v-tooltip="'Edit User'"
                  />
                  <Button 
                    :icon="slotProps.data.isBlocked ? 'pi pi-unlock' : 'pi pi-lock'" 
                    text 
                    rounded 
                    size="small" 
                    :severity="slotProps.data.isBlocked ? 'success' : 'warning'"
                    @click="toggleUserStatus(slotProps.data)"
                    :v-tooltip="slotProps.data.isBlocked ? 'Unblock User' : 'Block User'"
                  />
                  <Button 
                    icon="pi pi-trash" 
                    text 
                    rounded 
                    size="small" 
                    severity="danger"
                    @click="deleteUser(slotProps.data)"
                    v-tooltip="'Delete User'"
                  />
                </div>
              </template>
            </Column>
          </DataTable>
        </div>
      </div>
    </div>

    <!-- Mobile Filter Drawer -->
    <Sidebar 
      v-model:visible="showFilters" 
      position="left" 
      class="filter-drawer mobile-only"
      :style="{ width: '320px' }"
    >
      <template #header>
        <div class="drawer-header">
          <h3>
            <i class="pi pi-filter"></i>
            Filters
          </h3>
        </div>
      </template>

      <div class="drawer-content">
        <!-- Search -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-search"></i>
            Search
          </label>
          <InputText 
            v-model="searchTerm" 
            placeholder="Search users..." 
            class="w-full"
          />
        </div>

        <!-- Role Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-users"></i>
            Role
          </label>
          <MultiSelect
            v-model="selectedRoleFilter"
            :options="roleOptions"
            option-label="label"
            option-value="value"
            placeholder="Select roles"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Subscription Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-star"></i>
            Subscription
          </label>
          <MultiSelect
            v-model="selectedSubscriptionFilter"
            :options="subscriptionOptions"
            option-label="label"
            option-value="value"
            placeholder="Select subscriptions"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Status Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-check-circle"></i>
            Status
          </label>
          <MultiSelect
            v-model="selectedStatusFilter"
            :options="statusOptions"
            option-label="label"
            option-value="value"
            placeholder="Select status"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Verification Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-verified"></i>
            Verification
          </label>
          <MultiSelect
            v-model="verificationFilter"
            :options="verificationOptions"
            option-label="label"
            option-value="value"
            placeholder="Select verification"
            class="w-full"
            :max-selected-labels="3"
          />
        </div>

        <!-- Date Range Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-calendar"></i>
            Date Range
          </label>
          <Calendar
            v-model="dateRange"
            selection-mode="range"
            :manual-input="false"
            placeholder="Select date range"
            class="w-full"
          />
        </div>

        <!-- Statistics -->
        <div class="filter-stats">
          <h4>Statistics</h4>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">Total Users</span>
              <span class="stat-value">{{ userStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active</span>
              <span class="stat-value success">{{ userStats.active }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Verified</span>
              <span class="stat-value info">{{ userStats.verified }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Job Seekers</span>
              <span class="stat-value">{{ userStats.jobSeekers }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Employers</span>
              <span class="stat-value">{{ userStats.employers }}</span>
            </div>
          </div>
        </div>

        <!-- Clear Filters -->
        <Button 
          label="Clear All Filters"
          icon="pi pi-filter-slash" 
          severity="secondary"
          @click="clearFilters"
          :disabled="!searchTerm && selectedRoleFilter.length === 0 && selectedSubscriptionFilter.length === 0"
          class="w-full"
        />
      </div>
    </Sidebar>

    <!-- User Dialog -->
    <Dialog 
      v-model:visible="showUserDialog" 
      :header="isEditMode ? 'Edit User' : 'Add User'"
      :modal="true" 
      class="user-dialog"
      :style="{ width: '800px' }"
      :maximizable="true"
    >
      <div class="dialog-content">
        <div class="form-grid">
          <!-- Personal Information -->
          <div class="form-section">
            <h4>Personal Information</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="first-name">First Name *</label>
                <InputText 
                  id="first-name"
                  v-model="userForm.firstName" 
                  placeholder="Enter first name"
                  class="w-full"
                  :class="{ 'p-invalid': !userForm.firstName.trim() }"
                />
              </div>
              
              <div class="form-group">
                <label for="middle-name">Middle Name</label>
                <InputText 
                  id="middle-name"
                  v-model="userForm.middleName" 
                  placeholder="Enter middle name"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="last-name">Last Name *</label>
                <InputText 
                  id="last-name"
                  v-model="userForm.lastName" 
                  placeholder="Enter last name"
                  class="w-full"
                  :class="{ 'p-invalid': !userForm.lastName.trim() }"
                />
              </div>
              
              <div class="form-group">
                <label for="created-by">Created By</label>
                <InputText 
                  id="created-by"
                  v-model="userForm.createdBy" 
                  placeholder="Enter creator"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="form-section">
            <h4>Contact Information</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="user-email">Email *</label>
                <InputText 
                  id="user-email"
                  v-model="userForm.email" 
                  type="email"
                  placeholder="Enter email address"
                  class="w-full"
                  :class="{ 'p-invalid': !userForm.email.trim() }"
                />
              </div>
              
              <div class="form-group">
                <label for="user-phone">Phone</label>
                <InputText 
                  id="user-phone"
                  v-model="userForm.phone" 
                  placeholder="Enter phone number"
                  class="w-full"
                />
              </div>
            </div>
            
            <div class="form-row" v-if="!isEditMode">
              <div class="form-group">
                <label for="user-password">Password *</label>
                <Password 
                  id="user-password"
                  v-model="userForm.password" 
                  placeholder="Enter password"
                  class="w-full"
                  :class="{ 'p-invalid': !isEditMode && !userForm.password.trim() }"
                  toggle-mask
                />
              </div>
            </div>
          </div>

          <!-- Role & Subscription -->
          <div class="form-section">
            <h4>Role & Subscription</h4>
            
            <div class="form-row">
              <div class="form-group">
                <label for="user-role">Role</label>
                <Dropdown
                  id="user-role"
                  v-model="userForm.role"
                  :options="roleOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                />
              </div>
              
              <div class="form-group">
                <label for="user-subscription">Subscription Type</label>
                <Dropdown
                  id="user-subscription"
                  v-model="userForm.subscriptionType"
                  :options="subscriptionOptions"
                  option-label="label"
                  option-value="value"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- Verification Status -->
          <div class="form-section">
            <h4>Verification Status</h4>
            
            <div class="verification-checkboxes">
              <div class="checkbox-row">
                <div class="form-group checkbox-group">
                  <Checkbox 
                    id="email-verified"
                    v-model="userForm.isEmailVerified" 
                    :binary="true"
                  />
                  <label for="email-verified">Email Verified</label>
                </div>
                
                <div class="form-group checkbox-group">
                  <Checkbox 
                    id="phone-verified"
                    v-model="userForm.isPhoneVerified" 
                    :binary="true"
                  />
                  <label for="phone-verified">Phone Verified</label>
                </div>
              </div>
              
              <div class="checkbox-row">
                <div class="form-group checkbox-group">
                  <Checkbox 
                    id="aadhar-verified"
                    v-model="userForm.isAadharVerified" 
                    :binary="true"
                  />
                  <label for="aadhar-verified">Aadhar Verified</label>
                </div>
                
                <div class="form-group checkbox-group">
                  <Checkbox 
                    id="profile-complete"
                    v-model="userForm.isProfileComplete" 
                    :binary="true"
                  />
                  <label for="profile-complete">Profile Complete</label>
                </div>
              </div>
              
              <div class="checkbox-row">
                <div class="form-group checkbox-group">
                  <Checkbox 
                    id="user-blocked"
                    v-model="userForm.isBlocked" 
                    :binary="true"
                  />
                  <label for="user-blocked">Block User</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showUserDialog = false"
          />
          <Button 
            :label="isEditMode ? 'Update' : 'Create'" 
            @click="saveUser"
            :disabled="!userForm.email.trim() || !userForm.firstName.trim() || !userForm.lastName.trim() || (!isEditMode && !userForm.password.trim())"
          />
        </div>
      </template>
    </Dialog>

    <ConfirmDialog />
  </div>
</template>

<style scoped>
.users-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Page Header */
.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-toggle {
  display: none;
}

.header-title h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-title p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* Content Layout */
.content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Left Filters Sidebar (Desktop) */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease;
}

.filters-sidebar.collapsed {
  width: 60px;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collapse-btn {
  flex-shrink: 0;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.stat-value.success {
  color: var(--p-green-600);
}

.stat-value.info {
  color: var(--p-blue-600);
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Table Container */
.table-container {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.users-table {
  flex: 1;
  height: 100%;
}

/* User Cell Styling */
.user-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--p-primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.user-name {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.user-email {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.user-phone {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.verification-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.verification-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.score-bar {
  flex: 1;
  height: 6px;
}

.score-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
  min-width: 35px;
}

.verification-badges {
  display: flex;
  gap: 0.25rem;
}

.verification-badge {
  font-size: 0.75rem;
  padding: 0.125rem;
  border-radius: 3px;
}

.verification-badge.verified {
  color: var(--p-green-600);
  background: var(--p-green-100);
}

.created-date {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

/* Mobile Filter Drawer */
.filter-drawer {
  z-index: 1000;
}

.drawer-header {
  padding: 1rem 0;
}

.drawer-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.drawer-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Dialog Styling */
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem 0;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  border-bottom: 1px solid var(--p-surface-border);
  padding-bottom: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.verification-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkbox-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Responsive Design */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
  
  .filters-sidebar.collapsed {
    width: 60px;
  }
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: flex;
  }
  
  .filter-toggle {
    display: flex;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .table-container {
    padding: 1rem;
  }
  
  .form-row,
  .checkbox-row {
    grid-template-columns: 1fr;
  }
  
  .user-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar,
.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track,
.drawer-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb,
.drawer-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover,
.drawer-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track,
:global(.dark) .drawer-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb,
:global(.dark) .drawer-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover,
:global(.dark) .drawer-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>