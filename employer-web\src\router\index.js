import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Login from '@/views/Login.vue'
import Dashboard from '@/views/Dashboard.vue'
import JobsList from '@/views/JobsList.vue'
import JobDetails from '@/views/JobDetails.vue'
import SavedJobs from '@/views/SavedJobs.vue'
import Applications from '@/views/Applications.vue'
import Profile from '@/views/Profile.vue'
import Notifications from '@/views/Notifications.vue'
import CreateJob from '@/views/CreateJob.vue'

const routes = [
  // Landing page - main entry point
  {
    path: '/',
    name: 'Landing',
    redirect: '/employer/dashboard'
  },
  
  // Job seeker app routes
  {
    path: '/employer',
    redirect: '/employer/dashboard'
  },
  {
    path: '/employer/login',
    name: 'employerLogin',
    component: Login,
    meta: { requiresGuest: true, app: 'employer' }
  },
  {
    path: '/employer/dashboard',
    name: 'employerDashboard',
    component: Dashboard,
    meta: { requiresAuth: true, app: 'employer' }
  },
  {
    path: '/employer/jobs',
    name: 'JobSearch',
    component: JobsList,
    meta: { requiresAuth: true, app: 'employer' }
  },
  {
    path: '/employer/jobs/saved',
    name: 'SavedJobs',
    component: SavedJobs,
    meta: { requiresAuth: true, app: 'employer' }
  },
  {
    path: '/employer/jobs/:id',
    name: 'JobDetails',
    component: JobDetails,
    meta: { requiresAuth: true, app: 'employer' }
  },
  {
    path: '/employer/applications',
    name: 'MyApplications',
    component: Applications,
    meta: { requiresAuth: true, app: 'employer' }
  },
  {
    path: '/employer/profile',
    name: 'employerProfile',
    component: Profile,
    meta: { requiresAuth: true, app: 'employer' }
  },
  {
    path: '/employer/notifications',
    name: 'Notifications',
    component: Notifications,
    meta: { requiresAuth: true, app: 'employer' }
  },
  
  // Legacy redirects for backward compatibility
  {
    path: '/login',
    redirect: '/employer/login'
  },
  {
    path: '/dashboard',
    redirect: '/employer/dashboard'
  },
  {
    path: '/jobs',
    redirect: '/employer/jobs'
  },
  {
    path: '/employer/jobs/create',
    name: 'createJob',
    component: CreateJob,
    meta: { requiresAuth: true, app: 'employer' }
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (!authStore.isInitialised) {
    try{
    await authStore.initializeAuth()
    } catch(error) {
      console.log('Failed to get the profile')
    }
  }

  if (authStore.isAuthenticated) {
    if (!authStore.enums) {
      await authStore.getEnumData()
    }

    if (!authStore.industries) {
      await authStore.getIndustries()
    }
  }

  if (!authStore.isAuthenticated && to?.path !== '/employer/login') {
    next('/employer/login')
  } else {
    if (authStore.isAuthenticated && !authStore.isProfileComplete && to?.path !== '/employer/profile') {
      next('/employer/profile')
    } else {
      next()
    }
  }
})

export default router