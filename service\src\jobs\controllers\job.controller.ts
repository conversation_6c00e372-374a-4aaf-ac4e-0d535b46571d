import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JobService } from '../services/job.service';
import { CreateJobDto } from '../dto/create-job.dto';
import { UpdateJobDto } from '../dto/update-job.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { 
  JobEntity, 
  JobType, 
  PaymentType, 
  JobStatus, 
  JobUrgency, 
  ExperienceLevel, 
  ContactDisplayType,
  WorkSchedule,
  EducationLevel,
  LocationType,
  CompanySize,
  ApplicationStatus
} from '../entities/job.entity';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';

@ApiTags('Jobs')
@Controller('jobs')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class JobController {
  constructor(private readonly jobService: JobService) {}

  @Get('types')
  @ApiOperation({ summary: 'Get list of job types' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job types retrieved successfully',
    type: ApiResponseDto,
  })
  async getJobTypes(): Promise<ApiResponseDto<any>> {
    try {
      const jobTypes = Object.values(JobType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(jobTypes, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'JOB_TYPES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('payment-types')
  @ApiOperation({ summary: 'Get list of payment types' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Payment types retrieved successfully',
    type: ApiResponseDto,
  })
  async getPaymentTypes(): Promise<ApiResponseDto<any>> {
    try {
      const paymentTypes = Object.values(PaymentType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(paymentTypes, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'PAYMENT_TYPES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('statuses')
  @ApiOperation({ summary: 'Get list of job statuses' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job statuses retrieved successfully',
    type: ApiResponseDto,
  })
  async getJobStatuses(): Promise<ApiResponseDto<any>> {
    try {
      const statuses = Object.values(JobStatus).map((status) => ({
        id: status,
        label: status
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(statuses, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'JOB_STATUSES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('urgency-levels')
  @ApiOperation({ summary: 'Get list of job urgency levels' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job urgency levels retrieved successfully',
    type: ApiResponseDto,
  })
  async getJobUrgencyLevels(): Promise<ApiResponseDto<any>> {
    try {
      const urgencyLevels = Object.values(JobUrgency).map((urgency) => ({
        id: urgency,
        label: urgency
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(urgencyLevels, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'JOB_URGENCY_LEVELS_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('experience-levels')
  @ApiOperation({ summary: 'Get list of experience levels' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Experience levels retrieved successfully',
    type: ApiResponseDto,
  })
  async getExperienceLevels(): Promise<ApiResponseDto<any>> {
    try {
      const experienceLevels = Object.values(ExperienceLevel).map((level) => ({
        id: level,
        label: level
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(experienceLevels, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'EXPERIENCE_LEVELS_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('contact-display-types')
  @ApiOperation({ summary: 'Get list of contact display types' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Contact display types retrieved successfully',
    type: ApiResponseDto,
  })
  async getContactDisplayTypes(): Promise<ApiResponseDto<any>> {
    try {
      const contactDisplayTypes = Object.values(ContactDisplayType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(contactDisplayTypes, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'CONTACT_DISPLAY_TYPES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('work-schedules')
  @ApiOperation({ summary: 'Get list of work schedules' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Work schedules retrieved successfully',
    type: ApiResponseDto,
  })
  async getWorkSchedules(): Promise<ApiResponseDto<any>> {
    try {
      const workSchedules = Object.values(WorkSchedule).map((schedule) => ({
        id: schedule,
        label: schedule
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(workSchedules, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'WORK_SCHEDULES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('education-levels')
  @ApiOperation({ summary: 'Get list of education levels' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Education levels retrieved successfully',
    type: ApiResponseDto,
  })
  async getEducationLevels(): Promise<ApiResponseDto<any>> {
    try {
      const educationLevels = Object.values(EducationLevel).map((level) => ({
        id: level,
        label: level
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(educationLevels, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'EDUCATION_LEVELS_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('location-types')
  @ApiOperation({ summary: 'Get list of location types' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Location types retrieved successfully',
    type: ApiResponseDto,
  })
  async getLocationTypes(): Promise<ApiResponseDto<any>> {
    try {
      const locationTypes = Object.values(LocationType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(locationTypes, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'LOCATION_TYPES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('company-sizes')
  @ApiOperation({ summary: 'Get list of company sizes' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Company sizes retrieved successfully',
    type: ApiResponseDto,
  })
  async getCompanySizes(): Promise<ApiResponseDto<any>> {
    try {
      const companySizes = Object.values(CompanySize).map((size) => ({
        id: size,
        label: size
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(companySizes, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'COMPANY_SIZES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('application-statuses')
  @ApiOperation({ summary: 'Get list of application statuses' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Application statuses retrieved successfully',
    type: ApiResponseDto,
  })
  async getApplicationStatuses(): Promise<ApiResponseDto<any>> {
    try {
      const applicationStatuses = Object.values(ApplicationStatus).map((status) => ({
        id: status,
        label: status
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success(applicationStatuses, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'APPLICATION_STATUSES_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('job-enums')
  @ApiOperation({ summary: 'Get all job-related enums' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'All job-related enums retrieved successfully',
    type: ApiResponseDto,
  })
  async getAllJobEnums(): Promise<ApiResponseDto<any>> {
    try {
      const jobTypes = Object.values(JobType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const paymentTypes = Object.values(PaymentType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const statuses = Object.values(JobStatus).map((status) => ({
        id: status,
        label: status
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const urgencyLevels = Object.values(JobUrgency).map((urgency) => ({
        id: urgency,
        label: urgency
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const experienceLevels = Object.values(ExperienceLevel).map((level) => ({
        id: level,
        label: level
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const contactDisplayTypes = Object.values(ContactDisplayType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const workSchedules = Object.values(WorkSchedule).map((schedule) => ({
        id: schedule,
        label: schedule
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const educationLevels = Object.values(EducationLevel).map((level) => ({
        id: level,
        label: level
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const locationTypes = Object.values(LocationType).map((type) => ({
        id: type,
        label: type
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const companySizes = Object.values(CompanySize).map((size) => ({
        id: size,
        label: size
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      const applicationStatuses = Object.values(ApplicationStatus).map((status) => ({
        id: status,
        label: status
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      }));

      return ApiResponseDto.success({
        jobTypes,
        paymentTypes,
        statuses,
        urgencyLevels,
        experienceLevels,
        contactDisplayTypes,
        workSchedules,
        educationLevels,
        locationTypes,
        companySizes,
        applicationStatuses,
      }, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'JOB_ENUMS_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('employer/dashboard')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get employer dashboard data',
    description: 'Retrieves dashboard statistics and recent jobs for employers',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Dashboard data retrieved successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only employers, admins, and super admins can access dashboard',
    type: ApiResponseDto,
  })
  async getDashboard(@Request() req): Promise<ApiResponseDto<any>> {
    try {
      const dashboardData = await this.jobService.getDashboardData(req.user.id);
      return ApiResponseDto.success(dashboardData, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'DASHBOARD_DATA_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('jobseeker/dashboard')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({
    summary: 'Get job seeker dashboard data',
    description: 'Retrieves dashboard statistics and recent activities for job seekers',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Dashboard data retrieved successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only job seekers can access dashboard',
    type: ApiResponseDto,
  })
  async getJobSeekerDashboard(@Request() req): Promise<ApiResponseDto<any>> {
    try {
      const dashboardData = await this.jobService.getJobSeekerDashboardData(req.user.id);
      return ApiResponseDto.success(dashboardData, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'DASHBOARD_DATA_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post()
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Create a new job',
    description:
      'Creates a new job posting. Employers can create jobs that will be reviewed by admins before being published.',
  })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Job has been created successfully and is pending admin review',
    type: ApiResponseDto<JobEntity>,
  })
  @SwaggerResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - Invalid job data provided',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only employers, admins, and super admins can create jobs',
    type: ApiResponseDto,
  })
  async create(
    @Request() req,
    @Body() createJobDto: CreateJobDto,
  ): Promise<ApiResponseDto<JobEntity>> {
    try {
      const job = await this.jobService.create(req.user.id, createJobDto);
      return ApiResponseDto.success(job, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error('JOB_CREATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.JOB_SEEKER)
  @ApiOperation({
    summary: 'Get all jobs',
    description:
      'Retrieves a paginated list of jobs. For employers, shows their own jobs. For job seekers, shows all active jobs.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns a paginated list of jobs',
    type: ApiResponseDto<PaginatedResponseDto<JobEntity>>,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Invalid role access',
    type: ApiResponseDto,
  })
  async findAll(
    @Request() req,
    @Query() paginationDto: PaginationDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobEntity>>> {
    try {
      const jobs = await this.jobService.findAll(req.user.id, paginationDto);
      return ApiResponseDto.success(jobs, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('JOBS_FETCH_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('my-jobs')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get my posted jobs',
    description: 'Retrieves a paginated list of jobs posted by the current user.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns a paginated list of jobs posted by the user',
    type: ApiResponseDto<PaginatedResponseDto<JobEntity>>,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only employers, admins, and super admins can access their jobs',
    type: ApiResponseDto,
  })
  async findMyJobs(
    @Request() req,
    @Query() paginationDto: PaginationDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<JobEntity>>> {
    try {
      const jobs = await this.jobService.findMyJobs(req.user.id, paginationDto);
      return ApiResponseDto.success(jobs, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('MY_JOBS_FETCH_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get(':id')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.JOB_SEEKER)
  @ApiOperation({
    summary: 'Get a specific job',
    description: 'Retrieves detailed information about a specific job by ID.',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'The ID of the job to retrieve',
    type: String,
    format: 'uuid',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Returns the job details',
    type: ApiResponseDto<JobEntity>,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Invalid role access',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
    type: ApiResponseDto,
  })
  async findOne(@Param('id') id: string, @Request() req): Promise<ApiResponseDto<JobEntity>> {
    try {
      const job = await this.jobService.findOne(id, req.user.id);
      return ApiResponseDto.success(job, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('JOB_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Patch(':id')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Update a job',
    description:
      'Updates an existing job. Employers can only update their own jobs. Admins can update any job.',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'The ID of the job to update',
    type: String,
    format: 'uuid',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job has been updated successfully',
    type: ApiResponseDto<JobEntity>,
  })
  @SwaggerResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - Invalid update data provided',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only job creator or admin can update the job',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
    type: ApiResponseDto,
  })
  async update(
    @Param('id') id: string,
    @Request() req,
    @Body() updateJobDto: UpdateJobDto,
  ): Promise<ApiResponseDto<JobEntity>> {
    try {
      const job = await this.jobService.update(id, req.user.id, req.user.role, updateJobDto);
      return ApiResponseDto.success(job, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('JOB_UPDATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @Roles(UserRole.EMPLOYER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Delete a job',
    description:
      'Deletes a job. Employers can only delete their own jobs. Admins can delete any job.',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'The ID of the job to delete',
    type: String,
    format: 'uuid',
  })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Job has been deleted successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Authentication required',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Only job creator or admin can delete the job',
    type: ApiResponseDto,
  })
  @SwaggerResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
    type: ApiResponseDto,
  })
  async remove(@Param('id') id: string, @Request() req): Promise<ApiResponseDto<void>> {
    try {
      await this.jobService.remove(id, req.user.id, req.user.role);
      return ApiResponseDto.success(null, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('JOB_DELETE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
