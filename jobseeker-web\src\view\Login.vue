
<template>
  <div class="login-container">
    <div class="theme-toggle-wrapper">
      <ThemeToggle />
    </div>
    
    <div class="login-card">
      <!-- Company Logo -->
      <div class="logo-container">
        <div class="company-logo">
          <img src="../assets/jd-icon.png" alt="Logo" width="200" />
        </div>
      </div>

      <h1>Welcome Back</h1>
      <p class="subtitle">Please sign in to your account</p>

      <!-- Google Login Button -->
      <Button 
        @click="handleGoogleLogin" 
        class="google-login-button"
        severity="secondary"
        outlined
        size="large"
      >
        <i class="pi pi-google google-icon"></i>
        Continue with Google
      </Button>

      <!-- Divider -->
      <Divider align="center" type="solid" class="custom-divider">
        <span class="divider-text">or login with your email</span>
      </Divider>

      <!-- Email/OTP Form -->
      <form @submit.prevent="handleEmailSubmit" class="login-form">
        <!-- Email Input Phase -->
        <div v-if="!otpSent" class="form-group">
          <label for="email" class="input-label">Email</label>
          <InputText
            id="email"
            v-model="email"
            type="email"
            placeholder="Enter your email"
            class="custom-input"
            :class="{ 'p-invalid': emailError }"
            required
          />
          <small v-if="emailError" class="p-error">{{ emailError }}</small>
          
          <Button 
            type="submit" 
            class="primary-button"
            :loading="isLoading"
            :label="isLoading ? 'Sending OTP...' : 'Send OTP'"
            size="large"
          />
        </div>

        <!-- OTP Input Phase -->
        <div v-if="otpSent" class="form-group">
          <label class="input-label">Enter 6-digit OTP</label>
          <Message severity="info" class="custom-message">
            We've sent a verification code to {{ email }}
          </Message>
          
          <div class="otp-container">
            <InputText
              v-for="(digit, index) in otpDigits"
              :key="index"
              :ref="el => otpInputs[index] = el"
              v-model="otpDigits[index]"
              @input="handleOtpInput(index, $event)"
              @keydown="handleOtpKeydown(index, $event)"
              @paste="handleOtpPaste($event)"
              class="otp-input"
              maxlength="1"
              :class="{ 'filled': otpDigits[index] }"
            />
          </div>

          <div class="otp-actions">
            <Button 
              type="button" 
              @click="resendOtp"
              severity="secondary"
              outlined
              :disabled="resendCooldown > 0"
              :label="resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend OTP'"
              class="flex-1"
            />

            <Button 
              type="button" 
              @click="handleLogin"
              :disabled="!isOtpComplete || isLoading"
              :loading="isLoading"
              :label="isLoading ? 'Verifying...' : 'Login'"
              class="flex-1 primary-button"
            />
          </div>

          <Button 
            type="button" 
            @click="goBackToEmail"
            text
            severity="secondary"
            class="back-button"
          >
            <i class="pi pi-arrow-left"></i>
            Back to email
          </Button>
        </div>
      </form>

      <div class="form-footer" v-if="!otpSent">
        <a href="#" class="forgot-link">
          <i class="pi pi-question-circle"></i>
          Forgot your password?
        </a>
        <p class="signup-text">
          Don't have an account? 
          <a href="#" class="signup-link">Sign up</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Divider from 'primevue/divider'
import Message from 'primevue/message'
import ThemeToggle from './ThemeToggle.vue'

const email = ref('')
const emailError = ref('')
const otpDigits = ref(['', '', '', '', '', ''])
const otpInputs = ref([])
const isLoading = ref(false)
const otpSent = ref(false)
const resendCooldown = ref(0)

const isOtpComplete = computed(() => {
  return otpDigits.value.every(digit => digit.length === 1)
})

const handleGoogleLogin = () => {
  console.log('Google login clicked')
  alert('Google login would be implemented here')
}

const handleEmailSubmit = async () => {
  if (otpSent.value) return

  emailError.value = ''
  
  if (!email.value) {
    emailError.value = 'Email is required'
    return
  }

  isLoading.value = true

  // Simulate sending OTP
  setTimeout(() => {
    console.log('OTP sent to:', email.value)
    otpSent.value = true
    isLoading.value = false
    startResendCooldown()
    // Focus first OTP input
    nextTick(() => {
      if (otpInputs.value[0]) {
        otpInputs.value[0].$el.focus()
      }
    })
  }, 1000)
}

const handleOtpInput = (index, event) => {
  const value = event.target.value

  if (value && index < 5) {
    // Move to next input
    nextTick(() => {
      if (otpInputs.value[index + 1]) {
        otpInputs.value[index + 1].$el.focus()
      }
    })
  }
}

const handleOtpKeydown = (index, event) => {
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    // Move to previous input on backspace
    nextTick(() => {
      if (otpInputs.value[index - 1]) {
        otpInputs.value[index - 1].$el.focus()
      }
    })
  }
}

const handleOtpPaste = (event) => {
  event.preventDefault()
  const pastedData = event.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6)

  for (let i = 0; i < Math.min(pastedData.length, 6); i++) {
    otpDigits.value[i] = pastedData[i]
  }
}

const emit = defineEmits(['loginSuccess'])

const handleLogin = async () => {
  if (!isOtpComplete.value) return

  isLoading.value = true
  const otpCode = otpDigits.value.join('')

  // Simulate login verification
  setTimeout(() => {
    console.log('Login attempt with OTP:', { email: email.value, otp: otpCode })
    isLoading.value = false
    // Emit login success with user email
    emit('loginSuccess', { email: email.value })
  }, 1000)
}

const resendOtp = () => {
  if (resendCooldown.value > 0) return

  console.log('Resending OTP to:', email.value)
  alert('OTP resent!')
  startResendCooldown()
}

const startResendCooldown = () => {
  resendCooldown.value = 30
  const interval = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(interval)
    }
  }, 1000)
}

const goBackToEmail = () => {
  otpSent.value = false
  otpDigits.value = ['', '', '', '', '', '']
  resendCooldown.value = 0
  emailError.value = ''
}
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--login-bg-start) 0%, var(--login-bg-end) 100%);
  padding: 1rem;
  transition: background 0.3s ease;
  overflow: hidden;
}

.theme-toggle-wrapper {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

.login-card {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

:global(.dark-theme) .login-card {
  background: #2a2d3a;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.company-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-radius: 50%;
  background: rgba(0, 82, 204, 0.1);
}

.company-logo-icon {
  font-size: 3rem !important;
  color: #0052cc !important;
}

:global(.dark-theme) .company-logo-icon {
  color: #4299e1 !important;
}

h1 {
  text-align: center;
  margin-bottom: 0.5rem;
  color: #1a202c;
  font-size: 2rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

:global(.dark-theme) h1 {
  color: #e2e8f0;
}

.subtitle {
  text-align: center;
  color: #64748b;
  margin-bottom: 2rem;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

:global(.dark-theme) .subtitle {
  color: #94a3b8;
}

.google-login-button {
  width: 100% !important;
  margin-bottom: 1.5rem !important;
  height: 48px !important;
  border-radius: 8px !important;
}

.google-icon {
  margin-right: 0.75rem !important;
}

.custom-divider {
  margin: 1.5rem 0 !important;
}

.divider-text {
  padding: 0 1rem;
  color: #64748b;
  font-size: 0.85rem;
  background: white;
  transition: all 0.3s ease;
  font-weight: 500;
}

:global(.dark-theme) .divider-text {
  color: #94a3b8;
  background: #2a2d3a;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

:global(.dark-theme) .input-label {
  color: #e2e8f0;
}

.custom-input {
  width: 100% !important;
  height: 48px !important;
  border-radius: 8px !important;
  font-size: 1rem !important;
}

.custom-message {
  margin-bottom: 1rem !important;
  border-radius: 8px !important;
}

.otp-container {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin: 1rem 0 1.5rem 0;
}

.otp-input {
  width: 48px !important;
  height: 48px !important;
  text-align: center !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.otp-input.filled {
  border-color: #0052cc !important;
  background-color: rgba(0, 82, 204, 0.05) !important;
  transform: scale(1.05);
}

.otp-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.flex-1 {
  flex: 1;
}

.primary-button {
  background: linear-gradient(135deg, #0052cc 0%, #003d99 100%) !important;
  border-color: #0052cc !important;
  height: 48px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  width: 100% !important;
  margin-top: 0.5rem !important;
}

.back-button {
  align-self: center;
  margin-top: 1rem !important;
  gap: 0.5rem !important;
}

.form-footer {
  margin-top: 2rem;
  text-align: center;
}

.forgot-link {
  color: #0052cc;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
  font-weight: 500;
}

:global(.dark-theme) .forgot-link {
  color: #4299e1;
}

.forgot-link:hover {
  text-decoration: underline;
}

.signup-text {
  color: #64748b;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

:global(.dark-theme) .signup-text {
  color: #94a3b8;
}

.signup-link {
  color: #0052cc;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

:global(.dark-theme) .signup-link {
  color: #4299e1;
}

.signup-link:hover {
  text-decoration: underline;
}

/* Enhanced PrimeVue component styling */
:deep(.p-button) {
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

:deep(.p-button-large) {
  height: 48px !important;
}

:deep(.p-button:not(.p-button-outlined):not(.p-button-text)) {
  background: linear-gradient(135deg, #0052cc 0%, #003d99 100%) !important;
  border-color: #0052cc !important;
}

:deep(.p-button:not(.p-button-outlined):not(.p-button-text):hover) {
  background: linear-gradient(135deg, #003d99 0%, #002d73 100%) !important;
  border-color: #003d99 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 82, 204, 0.3) !important;
}

:deep(.p-inputtext) {
  border-radius: 8px !important;
  border: 2px solid #e2e8f0 !important;
  transition: all 0.2s ease !important;
  font-size: 1rem !important;
}

:deep(.p-inputtext:focus) {
  border-color: #0052cc !important;
  box-shadow: 0 0 0 3px rgba(0, 82, 204, 0.1) !important;
}

:deep(.p-button-outlined.p-button-secondary) {
  border-color: #e2e8f0 !important;
  color: #64748b !important;
}

:deep(.p-button-outlined.p-button-secondary:hover) {
  background: #0052cc !important;
  border-color: #0052cc !important;
  color: white !important;
}

:deep(.p-divider) {
  margin: 1.5rem 0 !important;
}

:deep(.p-divider .p-divider-content) {
  background: white !important;
}

:global(.dark-theme) :deep(.p-divider .p-divider-content) {
  background: #2a2d3a !important;
}

:deep(.p-message) {
  border-radius: 8px !important;
}

/* Dark theme overrides for PrimeVue */
:global(.dark-theme) :deep(.p-inputtext) {
  background: #374151 !important;
  border-color: #4b5563 !important;
  color: #e2e8f0 !important;
}

:global(.dark-theme) :deep(.p-inputtext:focus) {
  border-color: #4299e1 !important;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1) !important;
}

:global(.dark-theme) :deep(.p-button-outlined.p-button-secondary) {
  border-color: #4b5563 !important;
  color: #e2e8f0 !important;
}
</style>
