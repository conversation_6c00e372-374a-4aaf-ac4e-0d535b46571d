import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Query,
  Request,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../enums/user.enum';
import { UsersService } from '../services/users.service';
import { UserEntity } from '../entities/user.entity';
import { SubscriptionService, SubscriptionFeature } from '../subscription/subscription.service';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get all users' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'role', required: false, enum: UserRole })
  @ApiQuery({ name: 'isBlocked', required: false, type: Boolean })
  @ApiQuery({ name: 'searchTerm', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns a paginated list of users',
    type: ApiResponseDto<PaginatedResponseDto<UserEntity>>,
  })
  async getUsers(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('role') role?: UserRole,
    @Query('isBlocked') isBlocked?: boolean,
    @Query('searchTerm') searchTerm?: string,
  ): Promise<ApiResponseDto<PaginatedResponseDto<UserEntity>>> {
    try {
      const result = await this.usersService.findAll(page, limit, role, isBlocked, searchTerm);
      const paginatedResponse: PaginatedResponseDto<UserEntity> = {
        items: result.users,
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.page < result.totalPages,
        hasPreviousPage: result.page > 1,
      };
      return ApiResponseDto.success(paginatedResponse, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('USERS_FETCH_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns the user details',
    type: ApiResponseDto<UserEntity>,
  })
  async getUserById(@Param('id') id: string): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.findOne(id);
      return ApiResponseDto.success(user, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('USER_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Post()
  @Roles(UserRole.SUPER_ADMIN, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User created successfully',
    type: ApiResponseDto<UserEntity>,
  })
  async createUser(
    @Request() req,
    @Body() userData: Partial<UserEntity>,
  ): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.create(userData, req.user.id);
      return ApiResponseDto.success(user, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error('USER_CREATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User updated successfully',
    type: ApiResponseDto<UserEntity>,
  })
  async updateUser(
    @Request() req,
    @Param('id') id: string,
    @Body() userData: Partial<UserEntity>,
  ): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.update(id, userData, req.user.id);
      return ApiResponseDto.success(user, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('USER_UPDATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Delete user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User deleted successfully',
    type: ApiResponseDto<{ message: string }>,
  })
  async deleteUser(@Request() req, @Param('id') id: string): Promise<ApiResponseDto<{ message: string }>> {
    try {
      await this.usersService.delete(id, req.user.id);
      return ApiResponseDto.success({ message: 'User deleted successfully' }, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('USER_DELETE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('profile/me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns the current user profile',
    type: ApiResponseDto<UserEntity>,
  })
  async getMyProfile(@Request() req): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.findOne(req.user.id);
      return ApiResponseDto.success(user, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('PROFILE_FETCH_ERROR', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Put('profile/me')
  async updateMyProfile(@Request() req, @Body() userData: Partial<UserEntity>) {
    return this.usersService.update(req.user.id, userData, req.user.id);
  }

  @Post(':id/block')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Block user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User blocked successfully',
    type: ApiResponseDto<UserEntity>,
  })
  async blockUser(@Request() req, @Param('id') id: string): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.toggleBlock(id, req.user.id, 'block');
      return ApiResponseDto.success(user, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('USER_BLOCK_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post(':id/unblock')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Unblock user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User unblocked successfully',
    type: ApiResponseDto<UserEntity>,
  })
  async unblockUser(@Request() req, @Param('id') id: string): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.toggleBlock(id, req.user.id, 'unblock');
      return ApiResponseDto.success(user, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('USER_UNBLOCK_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Put(':id/role')
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update user role' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User role updated successfully',
    type: ApiResponseDto<UserEntity>,
  })
  async updateUserRole(
    @Request() req,
    @Param('id') id: string,
    @Body('role') role: UserRole,
  ): Promise<ApiResponseDto<UserEntity>> {
    try {
      const user = await this.usersService.updateRole(id, role, req.user.id);
      return ApiResponseDto.success(user, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('ROLE_UPDATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('subscription/features')
  async getSubscriptionFeatures(@Request() req): Promise<SubscriptionFeature[]> {
    const subscription = await this.subscriptionService.findByUserId(req.user.id);
    if (!subscription) {
      throw new NotFoundException('No subscription found for user');
    }
    return this.subscriptionService.getSubscriptionFeatures(subscription.id);
  }

  @Put('subscription/upgrade')
  async upgradeMySubscription(
    @Request() req,
    @Body('plan') plan: string,
    @Body('amount') amount: number,
  ) {
    const subscription = await this.subscriptionService.findByUserId(req.user.id);
    if (!subscription) {
      throw new NotFoundException('No subscription found for user');
    }
    return this.subscriptionService.upgradeSubscription(subscription.id, plan, amount, req.user.id);
  }

  @Put('subscription/upgrade/:id')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  async upgradeSubscription(
    @Request() req,
    @Param('id') id: string,
    @Body('plan') plan: string,
    @Body('amount') amount: number,
  ) {
    return this.subscriptionService.upgradeSubscription(id, plan, amount, req.user.id);
  }
}
