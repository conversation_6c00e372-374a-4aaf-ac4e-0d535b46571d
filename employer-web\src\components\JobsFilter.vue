<template>
  <div class="jobs-filter">
    <div class="filter-header">
      <h3>{{ t('jobs.filters') }}</h3>
      <Button 
        @click="clearAllFilters" 
        text 
        size="small" 
        severity="secondary"
        class="clear-filters-btn"
      >
        {{ t('jobs.clearAll') }}
      </Button>
    </div>
    
    <div class="filter-sections">
      <!-- Search Filter -->
      <div class="filter-section">
        <label class="filter-label">{{ t('common.search') }}</label>
        <InputText
          v-model="localFilters.search"
          @input="debouncedFilterUpdate"
          placeholder="Job title, company, skills..."
          class="filter-input"
        />
      </div>
      
      <!-- Location Filter -->
      <div class="filter-section">
        <label class="filter-label">{{ t('common.location') }}</label>
        <InputText
          v-model="localFilters.location"
          @input="debouncedFilterUpdate"
          placeholder="City, state, or remote"
          class="filter-input"
        />
      </div>
      
      <!-- Industry Filter -->
      <div class="filter-section">
        <label class="filter-label">{{ t('jobs.industry') }}</label>
        <Dropdown
          v-model="localFilters.industry"
          @change="updateFilters"
          :options="industryOptions"
          optionLabel="label"
          optionValue="value"
          :placeholder="t('jobs.selectIndustry')"
          class="filter-dropdown"
          showClear
        />
      </div>
      
      <!-- Job Type Filter -->
      <div class="filter-section">
        <label class="filter-label">{{ t('jobs.jobType') }}</label>
        <MultiSelect
          v-model="localFilters.jobType"
          @change="updateFilters"
          :options="jobTypeOptions"
          optionLabel="label"
          optionValue="value"
          :placeholder="t('jobs.selectJobType')"
          class="filter-multiselect"
          :maxSelectedLabels="2"
        />
      </div>
      
      <!-- Experience Level Filter -->
      <div class="filter-section">
        <label class="filter-label">{{ t('jobs.experienceLevel') }}</label>
        <Dropdown
          v-model="localFilters.experienceLevel"
          @change="updateFilters"
          :options="experienceLevelOptions"
          optionLabel="label"
          optionValue="value"
          :placeholder="t('jobs.selectExperience')"
          class="filter-dropdown"
          showClear
        />
      </div>
      
      <!-- Payment Type Filter -->
      <div class="filter-section">
        <label class="filter-label">Payment Type</label>
        <Dropdown
          v-model="localFilters.paymentType"
          @change="updateFilters"
          :options="paymentTypeOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Select Payment Type"
          class="filter-dropdown"
          showClear
        />
      </div>
      
      <!-- Salary Range Slider -->
      <div class="filter-section">
        <label class="filter-label">
          {{ t('jobs.salaryRange') }}: ₹{{ formatSalary(salaryRange[0]) }} - ₹{{ formatSalary(salaryRange[1]) }}
        </label>
        <Slider
          v-model="salaryRange"
          @change="handleSalaryChange"
          :min="0"
          :max="1000000"
          :step="10000"
          range
          class="salary-slider"
        />
        <div class="salary-labels">
          <span class="salary-label-min">₹0</span>
          <span class="salary-label-max">₹10L+</span>
        </div>
      </div>

      <!-- Urgency Filter -->
      <div class="filter-section">
        <label class="filter-label">Job Priority</label>
        <div class="checkbox-group">
          <div class="checkbox-item">
            <Checkbox 
              v-model="localFilters.isUrgent" 
              @change="updateFilters"
              :binary="true" 
              inputId="urgent-jobs" 
            />
            <label for="urgent-jobs">{{ t('jobs.urgent') }} Jobs Only</label>
          </div>
        </div>
      </div>

      <!-- Remote Work Filter -->
      <div class="filter-section">
        <label class="filter-label">{{ t('jobs.workType') }}</label>
        <div class="checkbox-group">
          <div class="checkbox-item">
            <Checkbox 
              v-model="localFilters.isRemote" 
              @change="updateFilters"
              :binary="true" 
              inputId="remote-work" 
            />
            <label for="remote-work">{{ t('jobs.remoteWork') }}</label>
          </div>
        </div>
      </div>

      <!-- Job Status Filter -->
      <div class="filter-section">
        <label class="filter-label">Job Status</label>
        <MultiSelect
          v-model="localFilters.status"
          @change="updateFilters"
          :options="jobStatusOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Select Status"
          class="filter-multiselect"
          :maxSelectedLabels="2"
        />
      </div>

      <!-- Minimum Vacancies Filter -->
      <div class="filter-section">
        <label class="filter-label">Minimum Vacancies</label>
        <InputNumber
          v-model="localFilters.minVacancies"
          @input="debouncedFilterUpdate"
          placeholder="Min positions"
          :min="1"
          :max="100"
          class="filter-input"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'
import MultiSelect from 'primevue/multiselect'
import InputNumber from 'primevue/inputnumber'
import Checkbox from 'primevue/checkbox'
import Slider from 'primevue/slider'
import Button from 'primevue/button'

const { t } = useI18n()

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:filters', 'clear-filters'])

// Local filters for immediate UI updates
const localFilters = ref({
  search: '',
  location: '',
  industry: null,
  jobType: [],
  experienceLevel: null,
  paymentType: null,
  status: [],
  isUrgent: null,
  isRemote: null,
  minVacancies: null,
  ...props.filters
})

// Salary range slider (in INR)
const salaryRange = ref([0, 1000000])

// Filter options based on actual job data
const industryOptions = [
  { label: 'Restaurant', value: 'restaurant' },
  { label: 'Construction', value: 'construction' },
  { label: 'Manufacturing', value: 'manufacturing' },
  { label: 'Logistics', value: 'logistics' },
  { label: 'Healthcare', value: 'healthcare' }
]

const jobTypeOptions = [
  { label: 'Full Time', value: 'FULL_TIME' },
  { label: 'Part Time', value: 'PART_TIME' },
  { label: 'Contract', value: 'CONTRACT' },
  { label: 'Temporary', value: 'TEMPORARY' }
]

const experienceLevelOptions = [
  { label: 'Fresher', value: 'FRESHER' },
  { label: 'Experienced', value: 'EXPERIENCED' },
  { label: 'Expert', value: 'EXPERT' },
  { label: 'Senior', value: 'SENIOR' }
]

const paymentTypeOptions = [
  { label: 'Monthly', value: 'MONTHLY' },
  { label: 'Weekly', value: 'WEEKLY' },
  { label: 'Daily', value: 'DAILY' },
  { label: 'Hourly', value: 'HOURLY' }
]

const jobStatusOptions = [
  { label: 'Active', value: 'ACTIVE' },
  { label: 'Draft', value: 'DRAFT' },
  { label: 'Closed', value: 'CLOSED' },
  { label: 'Paused', value: 'PAUSED' }
]

// Debounced filter update for search and other inputs
let filterTimeout = null
const debouncedFilterUpdate = () => {
  clearTimeout(filterTimeout)
  filterTimeout = setTimeout(() => {
    updateFilters()
  }, 500)
}

const handleSalaryChange = () => {
  localFilters.value.salaryMin = salaryRange.value[0]
  localFilters.value.salaryMax = salaryRange.value[1]
  debouncedFilterUpdate()
}

const formatSalary = (amount) => {
  if (amount >= 100000) {
    return Math.round(amount / 100000) + 'L'
  } else if (amount >= 1000) {
    return Math.round(amount / 1000) + 'K'
  }
  return amount.toLocaleString()
}

const updateFilters = () => {
  const filters = {
    ...localFilters.value,
    salaryMin: salaryRange.value[0],
    salaryMax: salaryRange.value[1]
  }
  
  emit('update:filters', filters)
}

const clearAllFilters = () => {
  localFilters.value = {
    search: '',
    location: '',
    industry: null,
    jobType: [],
    experienceLevel: null,
    paymentType: null,
    status: [],
    isUrgent: null,
    isRemote: null,
    minVacancies: null
  }
  salaryRange.value = [0, 1000000]
  emit('clear-filters')
}

// Watch for external filter changes
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...localFilters.value, ...newFilters }
}, { deep: true })
</script>

<style scoped>
.jobs-filter {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.filter-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.clear-filters-btn {
  color: var(--primary-color) !important;
}

.filter-sections {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.filter-input,
.filter-dropdown,
.filter-multiselect {
  width: 100% !important;
}

.salary-slider {
  margin: 1rem 0 0.5rem 0 !important;
}

.salary-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-item label {
  font-size: 0.9rem;
  color: var(--text-color);
  cursor: pointer;
}

@media (max-width: 768px) {
  .jobs-filter {
    position: static;
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>