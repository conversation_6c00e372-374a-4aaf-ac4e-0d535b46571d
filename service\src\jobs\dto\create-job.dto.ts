import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsUUID,
  IsArray,
  IsBoolean,
  IsInt,
  Min,
  IsEmail,
  IsPhoneNumber,
} from 'class-validator';
import {
  JobType,
  PaymentType,
  JobUrgency,
  ExperienceLevel,
  ContactDisplayType,
  JobStatus,
  WorkSchedule,
  EducationLevel,
  LocationType,
  CompanySize,
} from '../entities/job.entity';

export class CreateJobDto {
  @ApiProperty({ description: 'Job title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Job description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Industry ID' })
  @IsUUID()
  industryId: string;

  @ApiProperty({ description: 'Sub-industry ID' })
  @IsString()
  subIndustryId: string;

  @ApiProperty({ description: 'Salary amount' })
  @IsNumber()
  salary: number;

  @ApiProperty({
    description: 'Job type (FULL_TIME, PART_TIME, CONTRACT, or DAILY_WAGE)',
    enum: JobType,
  })
  @IsEnum(JobType)
  jobType: JobType;

  @ApiProperty({
    description: 'Payment frequency (DAILY, WEEKLY, BI_WEEKLY, or MONTHLY)',
    enum: PaymentType,
    default: PaymentType.MONTHLY,
  })
  @IsEnum(PaymentType)
  paymentType: PaymentType;

  @ApiProperty({ description: 'Job location coordinates', required: false })
  @IsString()
  location?: string;

  @ApiProperty({
    description: 'Job status',
    enum: JobStatus,
    default: JobStatus.PENDING,
  })
  @IsEnum(JobStatus)
  @IsOptional()
  status?: JobStatus = JobStatus.PENDING;

  @ApiProperty({
    description: 'Job urgency level',
    enum: JobUrgency,
    default: JobUrgency.FLEXIBLE,
  })
  @IsEnum(JobUrgency)
  @IsOptional()
  urgency?: JobUrgency;

  @ApiProperty({
    description: 'Required experience level',
    enum: ExperienceLevel,
    default: ExperienceLevel.FRESHER,
  })
  @IsEnum(ExperienceLevel)
  @IsOptional()
  experienceLevel?: ExperienceLevel;

  @ApiProperty({
    description: 'List of job benefits',
    type: [String],
    example: ['Health Insurance', 'Overtime Pay', 'Weekly Off'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  benefits?: string[];

  @ApiProperty({
    description: 'List of job requirements',
    type: [String],
    example: ['Valid Driver License', 'Safety Training Certificate'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requirements?: string[];

  @ApiProperty({
    description: 'List of job responsibilities',
    type: [String],
    example: ['Operating heavy machinery', 'Maintaining safety standards'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  responsibilities?: string[];

  @ApiProperty({
    description: 'Required skills for the job',
    type: [String],
    example: ['Heavy Equipment Operation', 'Safety Protocols'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];

  @ApiProperty({
    description: 'URL of the job thumbnail image',
    required: false,
  })
  @IsString()
  @IsOptional()
  thumbnail?: string;

  @ApiProperty({
    description: 'URLs of additional job images',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[];

  @ApiProperty({
    description: 'Whether to show contact information on job details',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  showContact?: boolean;

  @ApiProperty({
    description: 'Type of contact information to display',
    enum: ContactDisplayType,
    default: ContactDisplayType.NONE,
  })
  @IsEnum(ContactDisplayType)
  @IsOptional()
  contactDisplayType?: ContactDisplayType;

  @ApiProperty({
    description: 'Contact phone number',
    example: '+1234567890',
    required: false,
  })
  @IsPhoneNumber()
  @IsOptional()
  contactPhone?: string;

  @ApiProperty({
    description: 'Contact email address',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  contactEmail?: string;

  @ApiProperty({
    description: 'Name of the contact person',
    example: 'John Doe',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPerson?: string;

  @ApiProperty({
    description: 'Number of vacancies available',
    minimum: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  vacancies?: number;

  @ApiProperty({
    description: 'Working hours details',
    example: '8 hours per day, 6 days a week',
    required: false,
  })
  @IsString()
  @IsOptional()
  workingHours?: string;

  @ApiProperty({
    description: 'Accommodation details if provided',
    example: 'Shared accommodation provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  accommodation?: string;

  @ApiProperty({
    description: 'Transportation details if provided',
    example: 'Transportation allowance provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  transportation?: string;

  @ApiProperty({
    description: 'Food provision details',
    example: 'Lunch provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  foodProvided?: string;

  @ApiProperty({
    description: 'Safety equipment provided',
    example: 'Safety boots, helmet, and gloves provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  safetyEquipment?: string;

  @ApiProperty({
    description: 'Training details if provided',
    example: 'On-the-job training provided',
    required: false,
  })
  @IsString()
  @IsOptional()
  trainingProvided?: string;
}
