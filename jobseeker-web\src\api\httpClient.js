import axios from 'axios'
import { API_CONFIG } from './config'
import i18n from '@/i18n'


// Create axios instance
const httpClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true
})

// Request interceptor to add auth token
httpClient.interceptors.request.use(
  (config) => {
    // Use emp_ prefixed token for job seeker authentication
    const token = localStorage.getItem('emp_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    config.headers.jdu = 'JOB_SEEKER'
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
httpClient.interceptors.response.use(
  (response) => {
    return response.data
  },
  async (error) => {
    const originalRequest = error.config

    // Handle 401 Unauthorized
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      // Clear invalid token (job seeker specific)
      localStorage.removeItem('emp_token')
      localStorage.removeItem('emp_user')
      
      // Redirect to login if not already there
      // if (!window.location.pathname.includes('/login')) {
      //   window.location.href = '/jobseeker/login'
      // }
      
      return Promise.reject(error)
    }

    // Handle network errors with retry logic
    if (!error.response && originalRequest._retryCount < API_CONFIG.retries) {
      originalRequest._retryCount = originalRequest._retryCount || 0
      originalRequest._retryCount++
      
      const delay = API_CONFIG.retryDelay * Math.pow(2, originalRequest._retryCount - 1)
      await new Promise(resolve => setTimeout(resolve, delay))
      
      return httpClient(originalRequest)
    }

    const responseData = error.response?.data || {};

    // Format error response
    // const errorMessage = error.response?.data?.message || 
    //                     error.response?.data?.error || 
    //                     error.message || 
    //                     'An unexpected error occurred'

    if (responseData && responseData.message) {
      for (let i = 0; i < responseData.message.length; i++) {
        responseData.message[i] = i18n.global.t(`apiMessage.${responseData.message[i]}`)
      }
    } 

    if (responseData.statusCode === 400) {
      responseData.success = false;
    }
    // const apiError = new Error(errorMessage)
    // apiError.status = error.response?.status || 500
    // apiError.code = error.response?.data?.code || 'API_ERROR'
    // apiError.data = error.response?.data

    return Promise.reject(responseData)
  }
)

export default httpClient
export { API_CONFIG }