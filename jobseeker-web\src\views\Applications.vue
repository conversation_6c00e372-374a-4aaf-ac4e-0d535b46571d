<template>
  <AppLayout>
    <div class="applications-page">
      <div class="applications-content">
        <!-- <PERSON> Header -->
        <div class="page-header">
          <div class="header-content">
            <h1>{{ t('applications.myApplications') }}</h1>
            <p>{{ t('applications.trackApplications') }}</p>
          </div>
        </div>

        <!-- Applications Stats -->
        <div class="stats-overview">
          <div class="stat-card total">
            <div class="stat-icon">
              <i class="pi pi-send"></i>
            </div>
            <div class="stat-content">
              <h3>{{ applications.length }}</h3>
              <p>{{ t('applications.totalApplications') }}</p>
            </div>
          </div>
          <div class="stat-card pending">
            <div class="stat-icon">
              <i class="pi pi-clock"></i>
            </div>
            <div class="stat-content">
              <h3>{{ pendingApplications.length }}</h3>
              <p>{{ t('applications.pendingReview') }}</p>
            </div>
          </div>
          <div class="stat-card interviews">
            <div class="stat-icon">
              <i class="pi pi-users"></i>
            </div>
            <div class="stat-content">
              <h3>{{ interviewApplications.length }}</h3>
              <p>{{ t('applications.interviews') }}</p>
            </div>
          </div>
          <div class="stat-card offers">
            <div class="stat-icon">
              <i class="pi pi-check-circle"></i>
            </div>
            <div class="stat-content">
              <h3>{{ offerApplications.length }}</h3>
              <p>{{ t('applications.jobOffers') }}</p>
            </div>
          </div>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
          <Button 
            @click="activeFilter = 'all'"
            :class="['filter-tab', { active: activeFilter === 'all' }]"
            text
          >
            {{ t('applications.allApplications', { count: applications.length }) }}
          </Button>
          <Button 
            @click="activeFilter = 'pending'"
            :class="['filter-tab', { active: activeFilter === 'pending' }]"
            text
          >
            {{ t('applications.pending', { count: pendingApplications.length }) }}
          </Button>
          <Button 
            @click="activeFilter = 'interview'"
            :class="['filter-tab', { active: activeFilter === 'interview' }]"
            text
          >
            {{ t('applications.interview', { count: interviewApplications.length }) }}
          </Button>
          <Button 
            @click="activeFilter = 'offer'"
            :class="['filter-tab', { active: activeFilter === 'offer' }]"
            text
          >
            {{ t('applications.offers', { count: offerApplications.length }) }}
          </Button>
          <Button 
            @click="activeFilter = 'rejected'"
            :class="['filter-tab', { active: activeFilter === 'rejected' }]"
            text
          >
            {{ t('applications.rejected', { count: rejectedApplications.length }) }}
          </Button>
        </div>

        <!-- Applications List -->
        <div class="applications-list" v-if="filteredApplications.length > 0">
          <div 
            v-for="application in filteredApplications" 
            :key="application.id"
            class="application-card"
          >
            <div class="application-header">
              <div class="job-info">
                <div class="job-avatar">
                  <Avatar 
                    :label="application.company.charAt(0).toUpperCase()" 
                    :style="{ backgroundColor: application.color }"
                    shape="circle"
                    size="large"
                  />
                </div>
                <div class="job-details">
                  <h3 class="job-title">{{ application.jobTitle }}</h3>
                  <p class="job-company">{{ application.company }}</p>
                  <p class="job-location">
                    <i class="pi pi-map-marker"></i>
                    {{ application.location }}
                  </p>
                </div>
              </div>
              <div class="application-status">
                <Tag 
                  :value="application.status" 
                  :severity="getStatusSeverity(application.status)"
                  class="status-tag"
                />
                <span class="application-date">
                  {{ t('applications.appliedAgo', { time: formatDate(application.appliedDate) }) }}
                </span>
              </div>
            </div>

            <div class="application-content">
              <div class="application-timeline">
                <div 
                  v-for="event in application.timeline" 
                  :key="event.id"
                  :class="['timeline-item', { active: event.isActive }]"
                >
                  <div class="timeline-icon">
                    <i :class="getTimelineIcon(event.type)"></i>
                  </div>
                  <div class="timeline-content">
                    <h4>{{ event.title }}</h4>
                    <p>{{ event.description }}</p>
                    <span class="timeline-date">{{ formatDate(event.date) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="application-actions">
              <Button 
                @click="viewJobDetails(application)"
                icon="pi pi-eye"
                :label="t('applications.viewJob')"
                text
                size="small"
              />
              <Button 
                @click="withdrawApplication(application)"
                icon="pi pi-times"
                :label="t('applications.withdraw')"
                text
                size="small"
                severity="danger"
                v-if="application.status === 'pending' || application.status === 'under_review'"
              />
              <Button 
                @click="viewDetails(application)"
                icon="pi pi-info-circle"
                :label="t('applications.details')"
                text
                size="small"
              />
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else class="empty-state">
          <i class="pi pi-send empty-icon"></i>
          <h3>{{ getEmptyStateTitle() }}</h3>
          <p>{{ getEmptyStateMessage() }}</p>
          <Button 
            @click="searchJobs"
            icon="pi pi-search"
            :label="t('applications.findJobsToApply')"
            v-if="activeFilter === 'all'"
          />
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="loading-state">
          <i class="pi pi-spin pi-spinner loading-icon"></i>
          <p>{{ t('applications.loadingApplications') }}</p>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import Tag from 'primevue/tag'
import AppLayout from '@/components/AppLayout.vue'

const router = useRouter()
const { t } = useI18n()
const isLoading = ref(false)
const activeFilter = ref('all')

// Mock applications data
const applications = ref([
  {
    id: 1,
    jobTitle: 'Construction Worker',
    company: 'ABC Construction',
    location: 'New York, NY',
    status: 'interview',
    appliedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#3b82f6',
    timeline: [
      {
        id: 1,
        type: 'applied',
        title: t('applications.applicationSubmitted'),
        description: t('applications.applicationSubmittedDesc'),
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 2,
        type: 'review',
        title: t('applications.underReview'),
        description: t('applications.underReviewDesc'),
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 3,
        type: 'interview',
        title: t('applications.interviewScheduled'),
        description: t('applications.interviewScheduledDesc'),
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      }
    ]
  },
  {
    id: 2,
    jobTitle: 'Warehouse Associate',
    company: 'XYZ Logistics',
    location: 'Los Angeles, CA',
    status: 'pending',
    appliedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#10b981',
    timeline: [
      {
        id: 1,
        type: 'applied',
        title: t('applications.applicationSubmitted'),
        description: t('applications.applicationSubmittedDesc'),
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      }
    ]
  },
  {
    id: 3,
    jobTitle: 'Maintenance Technician',
    company: 'Industrial Solutions',
    location: 'Chicago, IL',
    status: 'offer',
    appliedDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#f59e0b',
    timeline: [
      {
        id: 1,
        type: 'applied',
        title: t('applications.applicationSubmitted'),
        description: t('applications.applicationSubmittedDesc'),
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 2,
        type: 'review',
        title: t('applications.underReview'),
        description: t('applications.underReviewDesc'),
        date: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 3,
        type: 'interview',
        title: t('applications.interviewCompleted'),
        description: t('applications.interviewCompletedDesc'),
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 4,
        type: 'offer',
        title: t('applications.jobOfferReceived'),
        description: t('applications.jobOfferReceivedDesc'),
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      }
    ]
  },
  {
    id: 4,
    jobTitle: 'Security Guard',
    company: 'SecureTech Services',
    location: 'Miami, FL',
    status: 'rejected',
    appliedDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    color: '#ef4444',
    timeline: [
      {
        id: 1,
        type: 'applied',
        title: t('applications.applicationSubmitted'),
        description: t('applications.applicationSubmittedDesc'),
        date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 2,
        type: 'review',
        title: t('applications.underReview'),
        description: t('applications.underReviewDesc'),
        date: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      },
      {
        id: 3,
        type: 'rejected',
        title: t('applications.applicationNotSelected'),
        description: t('applications.applicationNotSelectedDesc'),
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true
      }
    ]
  }
])

// Computed filters
const pendingApplications = computed(() => 
  applications.value.filter(app => app.status === 'pending' || app.status === 'under_review')
)

const interviewApplications = computed(() => 
  applications.value.filter(app => app.status === 'interview')
)

const offerApplications = computed(() => 
  applications.value.filter(app => app.status === 'offer')
)

const rejectedApplications = computed(() => 
  applications.value.filter(app => app.status === 'rejected')
)

const filteredApplications = computed(() => {
  switch (activeFilter.value) {
    case 'pending':
      return pendingApplications.value
    case 'interview':
      return interviewApplications.value
    case 'offer':
      return offerApplications.value
    case 'rejected':
      return rejectedApplications.value
    default:
      return applications.value
  }
})

const getStatusSeverity = (status) => {
  switch (status) {
    case 'pending':
    case 'under_review':
      return 'warning'
    case 'interview':
      return 'info'
    case 'offer':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'secondary'
  }
}

const getTimelineIcon = (type) => {
  switch (type) {
    case 'applied':
      return 'pi pi-send'
    case 'review':
      return 'pi pi-eye'
    case 'interview':
      return 'pi pi-users'
    case 'offer':
      return 'pi pi-check-circle'
    case 'rejected':
      return 'pi pi-times-circle'
    default:
      return 'pi pi-circle'
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '1 day ago'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  return `${Math.floor(diffDays / 30)} months ago`
}

const getEmptyStateTitle = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applications.noPendingApplications')
    case 'interview':
      return t('applications.noInterviewInvitations')
    case 'offer':
      return t('applications.noJobOffersYet')
    case 'rejected':
      return t('applications.noRejectedApplications')
    default:
      return t('applications.noApplicationsYet')
  }
}

const getEmptyStateMessage = () => {
  switch (activeFilter.value) {
    case 'pending':
      return t('applications.allReviewed')
    case 'interview':
      return t('applications.keepApplyingInterviews')
    case 'offer':
      return t('applications.keepApplyingOffers')
    case 'rejected':
      return t('applications.noRejections')
    default:
      return t('applications.startApplying')
  }
}

const searchJobs = () => {
  router.push('/jobseeker/jobs')
}

const viewJobDetails = (application) => {
  // This would navigate to the job details if the job still exists
  console.log('View job details for:', application.jobTitle)
}

const withdrawApplication = (application) => {
  if (confirm(t('applications.withdrawConfirm', { job: application.jobTitle }))) {
    const index = applications.value.findIndex(app => app.id === application.id)
    if (index !== -1) {
      applications.value.splice(index, 1)
    }
  }
}

const viewDetails = (application) => {
  console.log('View application details for:', application.jobTitle)
  // TODO: Implement application details modal or page
}

onMounted(() => {
  // Load applications data
})
</script>

<style scoped>
.applications-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.applications-content {
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.header-content p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1.75rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--surface-border);
  overflow-x: auto;
}

.filter-tab {
  padding: 1rem 1.5rem !important;
  border-bottom: 2px solid transparent !important;
  color: var(--text-color-secondary) !important;
  font-weight: 500 !important;
  white-space: nowrap;
  transition: all var(--transition-duration) ease !important;
}

.filter-tab:hover {
  color: var(--primary-color) !important;
  background: var(--surface-hover) !important;
}

.filter-tab.active {
  color: var(--primary-color) !important;
  border-bottom-color: var(--primary-color) !important;
  background: var(--highlight-bg) !important;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.application-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all var(--transition-duration) ease;
}

.application-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.job-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.job-details {
  flex: 1;
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.job-company {
  color: var(--primary-color);
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.job-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.job-location i {
  color: var(--primary-color);
}

.application-status {
  text-align: right;
}

.status-tag {
  margin-bottom: 0.5rem;
}

.application-date {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.application-timeline {
  margin-bottom: 1.5rem;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  opacity: 0.6;
  transition: opacity var(--transition-duration) ease;
}

.timeline-item.active {
  opacity: 1;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--surface-200);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  flex-shrink: 0;
}

.timeline-item.active .timeline-icon {
  background: var(--primary-color);
  color: white;
}

.timeline-content {
  flex: 1;
}

.timeline-content h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 0.95rem;
  font-weight: 600;
}

.timeline-content p {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
}

.timeline-date {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.application-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .applications-content {
    padding: 1rem;
  }

  .header-content h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-tabs {
    gap: 0.25rem;
  }

  .filter-tab {
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  .application-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .application-status {
    text-align: left;
    width: 100%;
  }

  .application-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }
}
</style>