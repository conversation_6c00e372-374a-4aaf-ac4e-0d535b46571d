<script setup>
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { useTheme } from '../composables/useTheme'
import Dropdown from 'primevue/dropdown'
import ToggleButton from 'primevue/togglebutton'
import Menu from 'primevue/menu'

const route = useRoute()
const router = useRouter()
const { currentUser, logout } = useAuth()
const { isDarkMode, theme, availableThemes, toggleDarkMode, setTheme } = useTheme()

// Sidebar collapse state
const isCollapsed = ref(false)

// User menu
const userMenu = ref()
const userMenuItems = ref([
  {
    label: 'Profile',
    icon: 'pi pi-user',
    command: () => {
      // Handle profile navigation
    }
  },
  {
    label: 'Settings',
    icon: 'pi pi-cog',
    command: () => {
      router.push('/jdAdmin/settings')
    }
  },
  {
    separator: true
  },
  {
    label: 'Logout',
    icon: 'pi pi-sign-out',
    command: () => {
      logout()
    }
  }
])

const menuItems = computed(() => [
  { 
    id: 'dashboard', 
    label: 'Dashboard', 
    icon: 'pi pi-home', 
    route: '/jdAdmin/dashboard',
    active: route.name === 'Dashboard'
  },
  { 
    id: 'users', 
    label: 'Users', 
    icon: 'pi pi-users', 
    route: '/jdAdmin/users',
    active: route.name === 'Users'
  },
  { 
    id: 'industries', 
    label: 'Industries', 
    icon: 'pi pi-building', 
    route: '/jdAdmin/industries',
    active: route.name === 'Industries'
  },
  { 
    id: 'jobs', 
    label: 'Jobs', 
    icon: 'pi pi-briefcase', 
    route: '/jdAdmin/jobs',
    active: route.name === 'Jobs'
  },
  { 
    id: 'feedback', 
    label: 'Feedback', 
    icon: 'pi pi-comments', 
    route: '/jdAdmin/feedback',
    active: route.name === 'Feedback'
  },
  { 
    id: 'welcome-page', 
    label: 'Welcome Page', 
    icon: 'pi pi-window-maximize', 
    route: '/jdAdmin/welcome-page',
    active: route.name === 'WelcomePage'
  },
  { 
    id: 'errors', 
    label: 'Error Tracking', 
    icon: 'pi pi-exclamation-triangle', 
    route: '/jdAdmin/errors',
    active: route.name === 'ErrorTracking'
  },
  { 
    id: 'analytics', 
    label: 'Analytics', 
    icon: 'pi pi-chart-line', 
    route: '/jdAdmin/analytics',
    active: route.name === 'Analytics'
  },
  { 
    id: 'settings', 
    label: 'Settings', 
    icon: 'pi pi-cog', 
    route: '/jdAdmin/settings',
    active: route.name === 'Settings'
  },
  { 
    id: 'files', 
    label: 'Files', 
    icon: 'pi pi-folder', 
    route: '/jdAdmin/files',
    active: route.name === 'Files'
  },
  { 
    id: 'messages', 
    label: 'Messages', 
    icon: 'pi pi-envelope', 
    route: '/jdAdmin/messages',
    active: route.name === 'Messages'
  }
])

const handleNavigation = (item) => {
  router.push(item.route)
}

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const toggleUserMenu = (event) => {
  userMenu.value.toggle(event)
}

const handleThemeChange = (event) => {
  setTheme(event.value)
}

// Define emits for parent component communication
const emit = defineEmits(['sidebar-toggle'])

// Watch for collapse state changes and emit to parent
const handleSidebarToggle = () => {
  toggleSidebar()
  emit('sidebar-toggle', isCollapsed.value)
}
</script>

<template>
  <aside :class="['sidebar', { collapsed: isCollapsed }]">
    <div class="sidebar-header">
      <div class="logo">
        <i class="pi pi-shield"></i>
        <span v-show="!isCollapsed" class="logo-text">Admin</span>
      </div>
      <button 
        @click="handleSidebarToggle"
        class="collapse-btn"
        :title="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
      >
        <i :class="isCollapsed ? 'pi pi-angle-right' : 'pi pi-angle-left'"></i>
      </button>
    </div>
    
    <nav class="sidebar-nav">
      <ul class="nav-list">
        <li v-for="item in menuItems" :key="item.id" class="nav-item">
          <button 
            @click="handleNavigation(item)"
            :class="['nav-link', { active: item.active }]"
            :title="isCollapsed ? item.label : ''"
          >
            <i :class="item.icon"></i>
            <span v-show="!isCollapsed" class="nav-text">{{ item.label }}</span>
            <div v-if="item.active" class="active-indicator"></div>
          </button>
        </li>
      </ul>
    </nav>
    
    <!-- Theme Controls -->
    <div class="theme-section" v-if="!isCollapsed">
      <div class="section-title">
        <i class="pi pi-palette"></i>
        <span>Theme</span>
      </div>
      <div class="theme-controls">
        <Dropdown
          :model-value="theme"
          :options="availableThemes"
          option-label="name"
          option-value="value"
          placeholder="Theme"
          @change="handleThemeChange"
          class="theme-dropdown"
        />
        <ToggleButton
          v-model="isDarkMode"
          on-icon="pi pi-moon"
          off-icon="pi pi-sun"
          @change="toggleDarkMode"
          class="theme-toggle"
          aria-label="Toggle dark mode"
        />
      </div>
    </div>

    <!-- Collapsed Theme Controls -->
    <div class="theme-section-collapsed" v-if="isCollapsed">
      <ToggleButton
        v-model="isDarkMode"
        on-icon="pi pi-moon"
        off-icon="pi pi-sun"
        @change="toggleDarkMode"
        class="theme-toggle-collapsed"
        aria-label="Toggle dark mode"
      />
    </div>
    
    <div class="sidebar-footer">
      <div class="user-profile" @click="toggleUserMenu" :class="{ collapsed: isCollapsed }">
        <div class="user-avatar">
          <i class="pi pi-user"></i>
        </div>
        <div v-show="!isCollapsed" class="user-info">
          <span class="user-name">{{ currentUser?.name || 'Admin User' }}</span>
          <span class="user-role">Administrator</span>
        </div>
        <i v-show="!isCollapsed" class="pi pi-chevron-down user-menu-icon"></i>
      </div>
      
      <Menu
        ref="userMenu"
        :model="userMenuItems"
        :popup="true"
        class="user-menu"
      />
    </div>
  </aside>
</template>

<style scoped>
.sidebar {
  width: 240px;
  height: 100vh;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--p-text-color);
  min-width: 0;
}

.logo i {
  width: 32px;
  height: 32px;
  background: var(--p-primary-color);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.logo-text {
  white-space: nowrap;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.collapse-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: var(--p-surface-100);
  color: var(--p-text-muted-color);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.collapse-btn:hover {
  background: var(--p-surface-200);
  color: var(--p-text-color);
}

:global(.dark) .collapse-btn {
  background: var(--p-surface-700);
}

:global(.dark) .collapse-btn:hover {
  background: var(--p-surface-600);
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  border-radius: 0;
  position: relative;
  min-height: 48px;
}

.nav-link:hover {
  background: var(--p-surface-hover);
  color: var(--p-text-color);
}

.nav-link.active {
  background: var(--p-primary-50);
  color: var(--p-primary-600);
  font-weight: 600;
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--p-primary-color);
}

:global(.dark) .nav-link.active {
  background: rgba(var(--p-primary-500-rgb), 0.1);
  color: var(--p-primary-400);
}

.nav-link i {
  font-size: 1rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  white-space: nowrap;
  overflow: hidden;
  transition: opacity 0.3s ease;
  flex: 1;
}

.active-indicator {
  width: 6px;
  height: 6px;
  background: var(--p-primary-color);
  border-radius: 50%;
  flex-shrink: 0;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 0.75rem;
}

.sidebar.collapsed .active-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.theme-section {
  padding: 1rem;
  border-top: 1px solid var(--p-surface-border);
  border-bottom: 1px solid var(--p-surface-border);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--p-text-muted-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.theme-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.theme-dropdown {
  width: 100%;
}

.theme-toggle {
  width: 100%;
  height: 2.5rem;
}

.theme-section-collapsed {
  padding: 1rem;
  border-top: 1px solid var(--p-surface-border);
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  justify-content: center;
}

.theme-toggle-collapsed {
  width: 2.5rem;
  height: 2.5rem;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--p-surface-border);
  position: relative;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: var(--p-surface-50);
  transition: all 0.2s ease;
  cursor: pointer;
}

.user-profile:hover {
  background: var(--p-surface-100);
}

.user-profile.collapsed {
  justify-content: center;
  padding: 0.75rem;
}

:global(.dark) .user-profile {
  background: var(--p-surface-800);
}

:global(.dark) .user-profile:hover {
  background: var(--p-surface-700);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: var(--p-primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
  flex: 1;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--p-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-menu-icon {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
  flex-shrink: 0;
}

.sidebar.collapsed .user-profile {
  justify-content: center;
}

/* Scrollbar styling for sidebar */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 240px;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar.collapsed {
    width: 240px;
  }
  
  .collapse-btn {
    display: none;
  }

  .theme-section-collapsed {
    display: none;
  }
}
</style>