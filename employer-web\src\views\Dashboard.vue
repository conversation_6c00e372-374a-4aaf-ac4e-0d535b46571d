<template>
  <AppLayout>
    <div class="dashboard-container">
      <div class="dashboard-content">
        <!-- Welcome Section -->
        <div class="welcome-section">
          <div class="welcome-content">
            <h1>{{ welcomeMessage }}</h1>
            <p v-if="userLocation" class="user-location">
              <i class="pi pi-map-marker"></i>
              {{ userLocation }}
            </p>
            <p v-else class="user-status">{{ t('dashboard.readyToExplore') }}</p>
          </div>
        </div>

        <!-- Job Seeker Stats Overview -->
        <div class="stats-overview">
          <div class="stat-card primary">
            <div class="stat-icon">
              <i class="pi pi-briefcase"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStore.stats.totalJobs || 0 }}</h3>
              <p>{{ t('dashboard.availableJobs') }}</p>
              <span class="stat-change positive" v-if="dashboardStore.stats.jobGrowthRate > 0">
                {{ t('dashboard.newThisMonth', { percent: dashboardStore.stats.jobGrowthRate }) }}
              </span>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-icon">
              <i class="pi pi-send"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStore.stats.totalApplications || 0 }}</h3>
              <p>{{ t('dashboard.applicationsSent') }}</p>
              <span class="stat-change">
                {{ t('dashboard.pendingReview', { count: dashboardStore.stats.pendingApplications || 0 }) }}
              </span>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-icon">
              <i class="pi pi-eye"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStore.stats.profileViews || 0 }}</h3>
              <p>{{ t('dashboard.profileViews') }}</p>
              <span class="stat-change">
                {{ t('dashboard.thisWeek') }}
              </span>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-icon">
              <i class="pi pi-heart"></i>
            </div>
            <div class="stat-content">
              <h3>{{ dashboardStore.stats.savedJobs || 0 }}</h3>
              <p>{{ t('dashboard.savedJobs') }}</p>
              <span class="stat-change">
                {{ t('dashboard.readyToApply') }}
              </span>
            </div>
          </div>
        </div>

        <!-- Main Content Grid -->
        <div class="content-grid">
          <!-- Recommended Jobs -->
          <div class="recommended-jobs-card">
            <div class="card-header">
              <i class="pi pi-star card-icon"></i>
              <h3>{{ t('dashboard.recommendedForYou') }}</h3>
              <Button 
                @click="searchJobs"
                text
                size="small"
                :label="t('dashboard.viewAll')"
                class="view-all-btn"
              />
            </div>
            <div class="jobs-list">
              <div 
                v-for="job in recommendedJobs" 
                :key="job.id"
                class="job-item"
                @click="viewJobDetails(job)"
              >
                <div class="job-avatar">
                  <Avatar 
                    :label="job.company.charAt(0).toUpperCase()" 
                    :style="{ backgroundColor: job.color }"
                    shape="circle"
                    size="normal"
                  />
                </div>
                <div class="job-info">
                  <h4 class="job-title">{{ job.title }}</h4>
                  <p class="job-company">{{ job.company }}</p>
                  <div class="job-meta">
                    <span class="job-location">
                      <i class="pi pi-map-marker"></i>
                      {{ job.location }}
                    </span>
                    <span class="job-salary">
                      <i class="pi pi-indian-rupee"></i>
                      ₹{{ job.salaryMin.toLocaleString() }} - ₹{{ job.salaryMax.toLocaleString() }}
                    </span>
                  </div>
                </div>
                <div class="job-actions">
                  <Button 
                    icon="pi pi-heart" 
                    text 
                    size="small"
                    @click.stop="saveJob(job)"
                    :v-tooltip="t('dashboard.saveJob')"
                  />
                  <Button 
                    icon="pi pi-send" 
                    size="small"
                    @click.stop="applyToJob(job)"
                    :v-tooltip="t('dashboard.applyNow')"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="activity-card">
            <div class="card-header">
              <i class="pi pi-clock card-icon"></i>
              <h3>{{ t('dashboard.recentActivity') }}</h3>
              <span class="last-updated" v-if="dashboardStore.lastUpdated">
                {{ t('dashboard.updated', { time: dashboardStore.getStatsAge() }) }}
              </span>
            </div>
            <div class="activity-list">
              <div 
                v-for="activity in recentActivity" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <i :class="activity.icon" :style="{ color: activity.color }"></i>
                </div>
                <div class="activity-content">
                  <p class="activity-title">{{ activity.title }}</p>
                  <p class="activity-description">{{ activity.description }}</p>
                  <span class="activity-time">{{ formatActivityTime(activity.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import Button from 'primevue/button'
import Avatar from 'primevue/avatar'
import AppLayout from '@/components/AppLayout.vue'

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// Computed properties for user display
const userFullName = computed(() => {
  const user = authStore.user
  if (!user) return ''
  
  // Try to get name from profile first
  if (user.profile?.firstName || user.profile?.lastName) {
    const firstName = user.profile.firstName || ''
    const lastName = user.profile.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }
  
  // Fallback to user.name if available
  if (user.name) {
    return user.name
  }
  
  // Fallback to user.firstName and user.lastName if available
  if (user.firstName || user.lastName) {
    const firstName = user.firstName || ''
    const lastName = user.lastName || ''
    return `${firstName} ${lastName}`.trim()
  }
  
  return ''
})

const welcomeMessage = computed(() => {
  const name = userFullName.value
  if (name) {
    return t('dashboard.welcomeBack', { name })
  }
  return t('dashboard.welcomeBack', { name: t('profile.employer') })
})

const userLocation = computed(() => {
  const user = authStore.user
  if (!user) return ''
  
  // Try to get location from profile
  if (user.profile?.city && user.profile?.state) {
    return `${user.profile.city}, ${user.profile.state}`
  }
  
  if (user.profile?.city) {
    return user.profile.city
  }
  
  // Fallback to user.location if available
  return user.location || ''
})

// Get data from dashboard store
const recommendedJobs = computed(() => 
  dashboardStore.stats.recommendedJobs || []
)

const recentActivity = computed(() => 
  dashboardStore.stats.recentActivity || []
)

const formatActivityTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMinutes = Math.floor((now - date) / (1000 * 60))
  
  if (diffMinutes < 1) return t('time.justNow')
  if (diffMinutes < 60) return t('time.minutesAgo', { count: diffMinutes })
  
  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return t('time.hoursAgo', { count: diffHours })
  
  const diffDays = Math.floor(diffHours / 24)
  return t('time.daysAgo', { count: diffDays })
}

const searchJobs = () => {
  router.push('/employer/jobs')
}

const viewJobDetails = (job) => {
  router.push(`/employer/jobs/${job.id}`)
}

const saveJob = (job) => {
  console.log('Save job:', job.title)
  // TODO: Implement save job functionality
}

const applyToJob = (job) => {
  router.push(`/employer/jobs/${job.id}?action=apply`)
}

onMounted(() => {
  dashboardStore.fetchStats()
  dashboardStore.startAutoRefresh()
})

onUnmounted(() => {
  dashboardStore.stopAutoRefresh()
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--dashboard-bg-start) 0%, var(--dashboard-bg-end) 100%);
  transition: background var(--transition-duration) ease;
}

.dashboard-content {
  padding: 2rem;
}

.welcome-section {
  margin-bottom: 2rem;
}

.welcome-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.user-location,
.user-status {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-location i {
  color: var(--primary-color);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.stat-card.success::before { background: var(--green-500); }
.stat-card.warning::before { background: var(--yellow-500); }
.stat-card.info::before { background: var(--blue-500); }

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0 0 0.5rem 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-change {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.stat-change.positive {
  color: var(--green-500);
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.recommended-jobs-card,
.activity-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.card-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.card-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
}

.view-all-btn {
  color: var(--primary-color) !important;
  padding: 0 !important;
}

.last-updated {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.job-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--surface-border);
  cursor: pointer;
  transition: all var(--transition-duration) ease;
}

.job-item:hover {
  border-color: var(--primary-color);
  background: var(--surface-hover);
}

.job-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.job-title:hover {
  color: var(--primary-color);
}

.job-company {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.job-location,
.job-salary {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.job-location i {
  color: var(--primary-color);
}

.job-salary {
  font-weight: 600;
  color: var(--primary-color);
}

.job-salary i {
  color: var(--primary-color);
}

.job-actions {
  display: flex;
  gap: 0.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color var(--transition-duration) ease;
}

.activity-item:hover {
  background: var(--surface-hover);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.9rem;
}

.activity-description {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  font-size: 0.8rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .job-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>