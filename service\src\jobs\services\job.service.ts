import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual } from 'typeorm';
import { JobEntity, JobStatus } from '../entities/job.entity';
import { UserEntity } from '../../users/entities/user.entity';
import { IndustryEntity } from '../../industries/entities/industry.entity';
import { CreateJobDto } from '../dto/create-job.dto';
import { UpdateJobDto } from '../dto/update-job.dto';
import { JobScheduleDto } from '../dto/job-schedule.dto';
import { UserRole } from '../../users/enums/user.enum';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';

@Injectable()
export class JobService {
  private readonly WAIT_TIME_MINUTES = 15;

  constructor(
    @InjectRepository(JobEntity)
    private readonly jobRepository: Repository<JobEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(IndustryEntity)
    private readonly industryRepository: Repository<IndustryEntity>,
  ) {}

  async create(employerId: string, createJobDto: CreateJobDto): Promise<JobEntity> {
    const employer = await this.userRepository.findOne({
      where: { id: employerId },
      relations: ['verification'],
    });

    if (!employer) {
      throw new NotFoundException('Employer not found');
    }

    // if (employer.role !== UserRole.EMPLOYER) {
    //   throw new BadRequestException('Only employers can create jobs');
    // }

    if (employer.isBlocked) {
      throw new BadRequestException('Contact admin to create jobs');
    }

    const industry = await this.industryRepository.findOne({
      where: { id: createJobDto.industryId, isActive: true },
    });

    if (!industry) {
      throw new NotFoundException('Industry not found or inactive');
    }

    // Calculate scheduled post time (15 minutes from now)
    const scheduledPostTime = new Date();
    scheduledPostTime.setMinutes(scheduledPostTime.getMinutes() + this.WAIT_TIME_MINUTES);

    const job = this.jobRepository.create({
      ...createJobDto,
      employer,
      industry,
      isUserVerified: employer.verification?.isAadharVerified || false,
      status: JobStatus.PENDING,
      scheduledPostTime,
    });

    return this.jobRepository.save(job);
  }

  async findAll(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [jobs, total] = await this.jobRepository.findAndCount({
      where: {
        isDeleted: false,
      },
      relations: ['employer', 'industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: jobs,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findMyJobs(
    userId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [jobs, total] = await this.jobRepository.findAndCount({
      where: {
        employer: { id: userId },
        isDeleted: false,
      },
      relations: ['industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: jobs,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findOne(id: string, employerId: string): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: {
        id,
        // employer: { id: employerId },
        isDeleted: false,
      },
      relations: ['industry'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    return job;
  }

  async update(
    id: string,
    userId: string,
    userRole: UserRole,
    updateJobDto: UpdateJobDto,
  ): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Check if user is admin or the job creator
    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      job.employer.id !== userId
    ) {
      throw new ForbiddenException('Only admin or job creator can update this job');
    }

    if (updateJobDto.industryId) {
      const industry = await this.industryRepository.findOne({
        where: { id: updateJobDto.industryId, isActive: true },
      });

      if (!industry) {
        throw new NotFoundException('Industry not found or inactive');
      }

      job.industry = industry;
    }

    // If job is being updated while pending, reset the scheduled post time
    if (job.status === JobStatus.PENDING) {
      const scheduledPostTime = new Date();
      scheduledPostTime.setMinutes(scheduledPostTime.getMinutes() + this.WAIT_TIME_MINUTES);
      job.scheduledPostTime = scheduledPostTime;
    }

    Object.assign(job, updateJobDto);
    return this.jobRepository.save(job);
  }

  async remove(id: string, userId: string, userRole: UserRole): Promise<void> {
    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Check if user is admin or the job creator
    if (
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN &&
      job.employer.id !== userId
    ) {
      throw new ForbiddenException('Only admin or job creator can delete this job');
    }

    job.isDeleted = true;
    await this.jobRepository.save(job);
  }

  // Method to activate pending jobs that have passed their wait time
  async activatePendingJobs(): Promise<void> {
    const now = new Date();
    await this.jobRepository.update(
      {
        status: JobStatus.PENDING,
        scheduledPostTime: LessThanOrEqual(now),
      },
      {
        status: JobStatus.ACTIVE,
      },
    );
  }

  async findAllPendingJobs(paginationDto: PaginationDto): Promise<PaginatedResponseDto<JobEntity>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const [jobs, total] = await this.jobRepository.findAndCount({
      where: {
        status: JobStatus.PENDING,
        isDeleted: false,
      },
      relations: ['employer', 'industry'],
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      items: jobs,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findPendingJobById(id: string): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: {
        id,
        status: JobStatus.PENDING,
        isDeleted: false,
      },
      relations: ['employer', 'industry'],
    });

    if (!job) {
      throw new NotFoundException('Pending job not found');
    }

    return job;
  }

  async scheduleJob(
    id: string,
    userRole: UserRole,
    scheduleDto: JobScheduleDto,
  ): Promise<JobEntity> {
    if (userRole !== UserRole.ADMIN && userRole !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Only admins can schedule jobs');
    }

    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    if (job.status !== JobStatus.PENDING) {
      throw new BadRequestException('Can only schedule pending jobs');
    }

    // Update job status and remove scheduled post time if job is being activated
    if (scheduleDto.status === JobStatus.ACTIVE) {
      job.scheduledPostTime = null;
    }

    job.status = scheduleDto.status;
    job.adminComment = scheduleDto.comment;

    return this.jobRepository.save(job);
  }

  async getJobScheduleHistory(id: string): Promise<JobEntity> {
    const job = await this.jobRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['employer', 'industry'],
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    return job;
  }

  async getDashboardData(userId: string) {
    // Get total jobs count
    const totalJobs = await this.jobRepository.count({
      where: {
        employer: { id: userId },
        isDeleted: false,
      },
    });

    // Get jobs by status
    const jobsByStatus = await this.jobRepository
      .createQueryBuilder('job')
      .select('job.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('job.employer.id = :userId', { userId })
      .andWhere('job.isDeleted = :isDeleted', { isDeleted: false })
      .groupBy('job.status')
      .getRawMany();

    // Get recent jobs (last 5)
    const recentJobs = await this.jobRepository.find({
      where: {
        employer: { id: userId },
        isDeleted: false,
      },
      relations: ['industry'],
      order: { createdAt: 'DESC' },
      take: 5,
    });

    return {
      statistics: {
        totalJobs,
        jobsByStatus: jobsByStatus.reduce((acc, curr) => {
          acc[curr.status] = parseInt(curr.count);
          return acc;
        }, {}),
      },
      recentActivity: {
        jobs: recentJobs,
      },
    };
  }

  async getJobSeekerDashboardData(userId: string) {
    // Get total active jobs count
    const totalActiveJobs = await this.jobRepository.count({
      where: {
        status: JobStatus.ACTIVE,
        isDeleted: false,
      },
    });

    // Get jobs by industry
    const jobsByIndustry = await this.jobRepository
      .createQueryBuilder('job')
      .select('industry.name', 'industry')
      .addSelect('COUNT(*)', 'count')
      .leftJoin('job.industry', 'industry')
      .where('job.status = :status', { status: JobStatus.ACTIVE })
      .andWhere('job.isDeleted = :isDeleted', { isDeleted: false })
      .groupBy('industry.name')
      .getRawMany();

    // Get recent active jobs (last 5)
    const recentJobs = await this.jobRepository.find({
      where: {
        status: JobStatus.ACTIVE,
        isDeleted: false,
      },
      relations: ['industry', 'employer'],
      order: { createdAt: 'DESC' },
      take: 5,
    });

    // Get jobs by experience level
    const jobsByExperience = await this.jobRepository
      .createQueryBuilder('job')
      .select('job.experienceLevel', 'experienceLevel')
      .addSelect('COUNT(*)', 'count')
      .where('job.status = :status', { status: JobStatus.ACTIVE })
      .andWhere('job.isDeleted = :isDeleted', { isDeleted: false })
      .groupBy('job.experienceLevel')
      .getRawMany();

    return {
      statistics: {
        totalActiveJobs,
        jobsByIndustry: jobsByIndustry.reduce((acc, curr) => {
          acc[curr.industry] = parseInt(curr.count);
          return acc;
        }, {}),
        jobsByExperience: jobsByExperience.reduce((acc, curr) => {
          acc[curr.experienceLevel] = parseInt(curr.count);
          return acc;
        }, {}),
      },
      recentActivity: {
        jobs: recentJobs,
      },
    };
  }
}
