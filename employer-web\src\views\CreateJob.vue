<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import { JobType, PaymentType, JobStatus, JobUrgency, ExperienceLevel, ContactDisplayType } from '@/constants/enums'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Chips from 'primevue/chips'
import InputNumber from 'primevue/inputnumber'
import Button from 'primevue/button'
import Card from 'primevue/card'
import Divider from 'primevue/divider'
import AppLayout from '@/components/AppLayout.vue'
import { useToast } from 'primevue/usetoast'
import Stepper from 'primevue/stepper';
import StepList from 'primevue/steplist';
import StepPanels from 'primevue/steppanels';
import StepItem from 'primevue/stepitem';
import Step from 'primevue/step';
import StepPanel from 'primevue/steppanel';
import Editor from 'primevue/editor';
import Select from 'primevue/select';
import axios from 'axios'
import httpClient from '@/api/httpClient'
import { ENDPOINTS } from '@/api/endpoints'
import { useAuthStore } from '@/stores/auth'
import ProgressSpinner from 'primevue/progressspinner';

const router = useRouter()
const toast = useToast()
const isSubmitting = ref(false)
const authStore = useAuthStore()
const isLoading = ref(true)

// Initialize job enums
const jobEnums = ref({
  jobTypes: [],
  paymentTypes: [],
  jobUrgencies: [],
  experienceLevels: [],
  contactTypes: [],
  urgencyLevels: []
})

const industries = ref([])
const subIndustries = ref([])

// Watch for enums to become available
watch(() => authStore.enums, (newEnums) => {
  if (newEnums) {
    console.log('Enums available:', newEnums) // Debug log
    jobEnums.value = {
      jobTypes: newEnums.jobTypes || [],
      paymentTypes: newEnums.paymentTypes || [],
      jobUrgencies: newEnums.jobUrgencies || [],
      experienceLevels: newEnums.experienceLevels || [],
      contactTypes: newEnums.contactDisplayTypes || [],
      urgencyLevels: newEnums.urgencyLevels || []
    }
    isLoading.value = false
  }
}, { immediate: true, deep: true })

// Watch for industries to become available
watch(() => authStore.industries, (newIndustries) => {
  if (newIndustries) {
    console.log('Industries available:', newIndustries) // Debug log
    industries.value = newIndustries
    isLoading.value = false
  }
}, { immediate: true, deep: true })

// Watch for industry changes to update sub-industries


const job = reactive({
  title: '',
  description: '',
  industryId: '',
  subIndustryId: '',
  salary: 0,
  jobType: null,
  paymentType: null,
  location: '',
  urgency: null,
  experienceLevel: null,
  benefits: [],
  requirements: [],
  responsibilities: [],
  skills: [],
  contactDisplayType: null,
  contactPhone: '',
  contactEmail: '',
  contactPerson: '',
  vacancies: null,
  workingHours: '',
  accommodation: '',
  transportation: '',
  foodProvided: '',
  safetyEquipment: '',
  trainingProvided: '',
})

const errors = reactive({
  title: '',
  description: '',
  industryId: '',
  subIndustryId: '',
  jobType: '',
  experienceLevel: '',
  location: '',
  contactPhone: '',
  contactEmail: ''
})

watch(() => job.industryId, (newIndustryId) => {
  if (newIndustryId) {
    const selectedIndustry = industries.value.find(ind => ind.id === newIndustryId)
    if (selectedIndustry) {
      subIndustries.value = selectedIndustry.subIndustries || []
      // Reset sub-industry selection when industry changes
      job.subIndustryId = null
    }
  } else {
    subIndustries.value = []
    job.subIndustryId = null
  }
})

const validateStep1 = () => {
  let isValid = true
  
  // Title validation
  if (!job.title.trim()) {
    errors.title = 'Title is required'
    isValid = false
  } else {
    errors.title = ''
  }

  // Description validation
  if (!job.description.trim()) {
    errors.description = 'Description is required'
    isValid = false
  } else {
    errors.description = ''
  }

  // Industry validation
  if (!job.industryId) {
    errors.industryId = 'Industry is required'
    isValid = false
  } else {
    errors.industryId = ''
  }

  // Sub-industry validation
  if (!job.subIndustryId) {
    errors.subIndustryId = 'Sub-industry is required'
    isValid = false
  } else {
    errors.subIndustryId = ''
  }

  // Job Type validation
  if (!job.jobType) {
    errors.jobType = 'Job type is required'
    isValid = false
  } else {
    errors.jobType = ''
  }

  // Experience Level validation
  if (!job.experienceLevel) {
    errors.experienceLevel = 'Experience level is required'
    isValid = false
  } else {
    errors.experienceLevel = ''
  }

  // Location validation
  if (!job.location.trim()) {
    errors.location = 'Location is required'
    isValid = false
  } else {
    errors.location = ''
  }

  return isValid
}

const handleNextStep = (currentStep: string, nextStep: string, activateCallback: (step: string) => void) => {
  if (currentStep === '1' && !validateStep1()) {
    toast.add({
      severity: 'error',
      summary: 'Validation Error',
      detail: 'Please fill in all required fields',
      life: 3000
    })
    return
  }
  // Add validation for other steps here when needed
  activateCallback(nextStep)
}

const validateStep4 = () => {
  let isValid = true

  // Only validate contact fields if they are filled in
  if (job.contactPhone && !isValidPhoneNumber(job.contactPhone)) {
    errors.contactPhone = 'Please enter a valid phone number'
    isValid = false
  } else {
    errors.contactPhone = ''
  }

  if (job.contactEmail && !isValidEmail(job.contactEmail)) {
    errors.contactEmail = 'Please enter a valid email address'
    isValid = false
  } else {
    errors.contactEmail = ''
  }

  return isValid
}

// Add validation helper functions
const isValidPhoneNumber = (phone: string) => {
  // Basic phone number validation - can be adjusted based on your requirements
  const phoneRegex = /^\+?[\d\s-]{10,}$/
  return phoneRegex.test(phone)
}

const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const submit = async () => {
  try {
    isSubmitting.value = true
    
    // Validate all steps
    if (!validateStep1() || !validateStep4()) {
      toast.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly',
        life: 3000
      })
      return
    }

    // Prepare the job data
    const jobData = {
      ...job
    }

    if (!jobData.contactPhone) {
      delete jobData.contactPhone
    }

    if (!jobData.contactEmail) {
      delete jobData.contactEmail
    }
    // Make API call to create job
    const response = await httpClient.post(ENDPOINTS.JOBS.CREATE, jobData)

    // Show success message
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Job created successfully',
      life: 3000
    })

    // Redirect to jobs list
    router.push('/jobs')
  } catch (error) {
    console.error('Error creating job:', error)
    
    // Show error message
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response?.data?.message || 'Failed to create job. Please try again.',
      life: 3000
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <AppLayout>
    <div class="create-job-page">
      <div class="create-job-content">
        <div class="job-main">
          <Card class="job-form-card">
            <template #title>
              <h1 class="page-title">Post a Job</h1>
            </template>
            <template #content>
              <div v-if="isLoading" class="loading-container">
                <ProgressSpinner />
                <span>Please wait...</span>
              </div>
              <Stepper v-else class="job-stepper" value="1">
                <!-- Step 1: Basic Information -->
                <StepItem value="1">
                  <Step>Basic Information</Step>
                  <StepPanel v-slot="{ activateCallback }">
                    <div class="step-content">
                      <div class="form-section">
                        <div class="form-content">
                          <div class="form-field">
                            <label>Job Title <span class="required">*</span></label>
                            <InputText 
                              v-model="job.title" 
                              placeholder="Enter job title" 
                              class="w-full" 
                              :class="{ 'p-invalid': errors.title }"
                              @blur="validateStep1"
                            />
                            <small class="p-error" v-if="errors.title">{{ errors.title }}</small>
                          </div>

                          <div class="form-field">
                            <label>Description <span class="required">*</span></label>
                            <Editor 
                              v-model="job.description" 
                              editorStyle="height: 200px"
                              :class="{ 'p-invalid': errors.description }"
                              @blur="validateStep1"
                            >
                              <template v-slot:toolbar>
                                <span class="ql-formats">
                                  <button v-tooltip.bottom="'Bold'" class="ql-bold"></button>
                                  <button v-tooltip.bottom="'Italic'" class="ql-italic"></button>
                                  <button v-tooltip.bottom="'Underline'" class="ql-underline"></button>
                                </span>
                              </template>
                            </Editor>
                            <small class="p-error" v-if="errors.description">{{ errors.description }}</small>
                          </div>

                          <div class="form-field">
                            <label>Industry <span class="required">*</span></label>
                            <Select 
                              v-model="job.industryId" 
                              :options="industries" 
                              optionLabel="name" 
                              optionValue="id" 
                              placeholder="Select industry"
                              :class="{ 'p-invalid': errors.industryId }"
                              @change="validateStep1"
                            />
                            <small class="p-error" v-if="errors.industryId">{{ errors.industryId }}</small>
                          </div>

                          <div class="form-field">
                            <label>Industry sub-category<span class="required">*</span></label>
                            <Select 
                              v-model="job.subIndustryId" 
                              :options="subIndustries" 
                              optionLabel="name" 
                              optionValue="id" 
                              placeholder="Select sub-industry"
                              :class="{ 'p-invalid': errors.subIndustryId }"
                              @change="validateStep1"
                              :disabled="!job.industryId"
                            />
                            <small class="p-error" v-if="errors.subIndustryId">{{ errors.subIndustryId }}</small>
                          </div>

                          <div class="form-field">
                            <label>Location <span class="required">*</span></label>
                            <InputText 
                              v-model="job.location" 
                              placeholder="Enter location" 
                              class="w-full"
                              :class="{ 'p-invalid': errors.location }"
                              @blur="validateStep1"
                            />
                            <small class="p-error" v-if="errors.location">{{ errors.location }}</small>
                          </div>

                          <div class="form-field">
                            <label>Job Type <span class="required">*</span></label>
                            <Select 
                              v-model="job.jobType" 
                              :options="jobEnums.jobTypes" 
                              optionLabel="label" 
                              optionValue="id" 
                              placeholder="Select job type"
                              :class="{ 'p-invalid': errors.jobType }"
                              @change="validateStep1"
                            />
                            <small class="p-error" v-if="errors.jobType">{{ errors.jobType }}</small>
                          </div>

                          <div class="form-field">
                            <label>Experience Level <span class="required">*</span></label>
                            <Select 
                              v-model="job.experienceLevel" 
                              :options="jobEnums.experienceLevels" 
                              optionLabel="label" 
                              optionValue="id" 
                              placeholder="Select experience level"
                              :class="{ 'p-invalid': errors.experienceLevel }"
                              @change="validateStep1"
                            />
                            <small class="p-error" v-if="errors.experienceLevel">{{ errors.experienceLevel }}</small>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="step-actions">
                      <Button label="Next" @click="handleNextStep('1', '2', activateCallback)" />
                    </div>
                  </StepPanel>
                </StepItem>

                <!-- Step 2: Compensation & Requirements -->
                <StepItem value="2">
                  <Step>Compensation & Requirements</Step>
                  <StepPanel v-slot="{ activateCallback }">
                    <div class="step-content">
                      <div class="form-section">
                        <div class="form-content">
                          <div class="form-field">
                            <label>Salary <span class="required">*</span></label>
                            <InputNumber v-model="job.salary" mode="currency" currency="INR" locale="en-IN" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Payment Type <span class="required">*</span></label>
                            <Select v-model="job.paymentType" :options="jobEnums.paymentTypes" optionLabel="label" optionValue="id" placeholder="Select payment type" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Urgency <span class="required">*</span></label>
                            <Select v-model="job.urgency" :options="jobEnums.urgencyLevels" optionLabel="label" optionValue="id" placeholder="Select urgency" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Requirements</label>
                            <Chips v-model="job.requirements" separator="," class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Responsibilities</label>
                            <Chips v-if="job.responsibilities.length > 0" v-model="job.responsibilities" separator="," class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Skills</label>
                            <Chips v-model="job.skills" separator="," class="w-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="step-actions">
                      <Button label="Back" severity="secondary" @click="activateCallback('1')" />
                      <Button label="Next" @click="handleNextStep('2', '3', activateCallback)" />
                    </div>
                  </StepPanel>
                </StepItem>

                <!-- Step 3: Benefits & Additional Information -->
                <StepItem value="3">
                  <Step>Benefits & Additional Information</Step>
                  <StepPanel v-slot="{ activateCallback }">
                    <div class="step-content">
                      <div class="form-section">
                        <div class="form-content">
                          <div class="form-field">
                            <label>Benefits</label>
                            <Chips v-model="job.benefits" separator="," class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Working Hours</label>
                            <InputText v-model="job.workingHours" placeholder="Enter working hours" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Accommodation</label>
                            <InputText v-model="job.accommodation" placeholder="Enter accommodation details" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Transportation</label>
                            <InputText v-model="job.transportation" placeholder="Enter transportation details" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Food Provided</label>
                            <InputText v-model="job.foodProvided" placeholder="Enter food details" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Safety Equipment</label>
                            <InputText v-model="job.safetyEquipment" placeholder="Enter safety equipment details" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Training Provided</label>
                            <InputText v-model="job.trainingProvided" placeholder="Enter training details" class="w-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="step-actions">
                      <Button label="Back" severity="secondary" @click="activateCallback('2')" />
                      <Button label="Next" @click="handleNextStep('3', '4', activateCallback)" />
                    </div>
                  </StepPanel>
                </StepItem>

                <!-- Step 4: Contact Information -->
                <StepItem value="4">
                  <Step>Contact Information</Step>
                  <StepPanel v-slot="{ activateCallback }">
                    <div class="step-content">
                      <div class="form-section">
                        <div class="form-content">
                          <div class="form-field">
                            <label>Contact Type <span class="required">*</span></label>
                            <Select v-model="job.contactDisplayType" :options="jobEnums.contactTypes" optionLabel="label" optionValue="id" placeholder="Select contact type" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Contact Person</label>
                            <InputText v-model="job.contactPerson" placeholder="Enter contact person name" class="w-full" />
                          </div>

                          <div class="form-field">
                            <label>Contact Phone</label>
                            <InputText 
                              v-model="job.contactPhone" 
                              placeholder="Enter contact phone" 
                              class="w-full"
                              :class="{ 'p-invalid': errors.contactPhone }"
                              @blur="validateStep4"
                            />
                            <small class="p-error" v-if="errors.contactPhone">{{ errors.contactPhone }}</small>
                          </div>

                          <div class="form-field">
                            <label>Contact Email</label>
                            <InputText 
                              v-model="job.contactEmail" 
                              placeholder="Enter contact email" 
                              class="w-full"
                              :class="{ 'p-invalid': errors.contactEmail }"
                              @blur="validateStep4"
                            />
                            <small class="p-error" v-if="errors.contactEmail">{{ errors.contactEmail }}</small>
                          </div>

                          <div class="form-field">
                            <label>Vacancies</label>
                            <InputNumber v-model="job.vacancies" class="w-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="step-actions">
                      <Button label="Back" severity="secondary" @click="activateCallback('3')" />
                      <Button 
                        label="Submit" 
                        @click="submit" 
                        :loading="isSubmitting"
                        :disabled="isSubmitting"
                      />
                    </div>
                  </StepPanel>
                </StepItem>
              </Stepper>
            </template>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
.create-job-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.job-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.job-form-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.job-stepper {
  display: flex;
  flex-direction: column;
  height: calc(88vh);
}

.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.form-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.form-content {
  display: grid;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 500;
  color: var(--text-color);
}

.step-actions {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--surface-card);
  padding: 1rem;
  border-top: 1px solid var(--surface-border);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  z-index: 10;
}
.p-stepitem.p-stepitem-active {
  overflow: hidden;
  overflow-y: auto;
}
@media (max-width: 768px) {
  .create-job-content {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .form-section {
    padding: 1rem;
  }

  .step-actions {
    flex-direction: column;
  }

  .step-actions button {
    width: 100%;
  }
}

.required {
  color: var(--red-500);
  margin-left: 2px;
}

.p-error {
  color: var(--red-500);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  height: 100%;
}

.p-invalid {
  border-color: var(--red-500) !important;
}
</style>
