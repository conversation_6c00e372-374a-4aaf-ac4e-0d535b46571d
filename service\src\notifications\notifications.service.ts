import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification, NotificationType, NotificationPriority } from './entities/notification.entity';
import { NotificationPreference } from './entities/notification-preference.entity';
import { UsersService } from '../users/users.service';
import { JobService } from '../jobs/services/job.service';
import { NotificationsGateway } from './notifications.gateway';
import { NotificationTemplatesService } from './notification-templates.service';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private notificationsRepository: Repository<Notification>,
    @InjectRepository(NotificationPreference)
    private preferencesRepository: Repository<NotificationPreference>,
    private usersService: UsersService,
    private jobsService: JobService,
    private notificationsGateway: NotificationsGateway,
    private templatesService: NotificationTemplatesService,
  ) {}

  async create(createNotificationDto: {
    userId: string;
    type: NotificationType;
    data: Record<string, any>;
  }): Promise<Notification> {
    const { userId, type, data } = createNotificationDto;

    // Get user preferences
    const preferences = await this.getUserPreferences(userId);
    if (!preferences) {
      throw new NotFoundException('User preferences not found');
    }

    // Check if user wants to receive this type of notification
    if (!preferences.inAppPreferences[type]) {
      return null;
    }

    // Get template for notification type
    const template = this.templatesService.getTemplate(type, data);

    const notification = this.notificationsRepository.create({
      type,
      title: template.title,
      message: template.message,
      data: JSON.stringify(data),
      isRead: false,
      recipientId: userId,
      priority: NotificationPriority.MEDIUM,
    });

    const savedNotification = await this.notificationsRepository.save(notification);

    // Send real-time notification if enabled
    if (preferences.inAppPreferences[type]) {
      this.notificationsGateway.sendToUser(userId, savedNotification);
    }

    return savedNotification;
  }

  async findAll(
    userId: string,
    options: {
      isRead?: boolean;
      type?: NotificationType;
      page?: number;
      limit?: number;
    } = {},
  ): Promise<{ items: Notification[]; total: number }> {
    const { isRead, type, page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.notificationsRepository
      .createQueryBuilder('notification')
      .where('notification.recipientId = :userId', { userId });

    if (isRead !== undefined) {
      queryBuilder.andWhere('notification.isRead = :isRead', { isRead });
    }

    if (type) {
      queryBuilder.andWhere('notification.type = :type', { type });
    }

    const [items, total] = await queryBuilder
      .orderBy('notification.createdAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return { items, total };
  }

  async markAsRead(id: string, userId: string): Promise<Notification> {
    const notification = await this.notificationsRepository.findOne({
      where: { id, recipientId: userId },
    });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    notification.isRead = true;
    const updatedNotification = await this.notificationsRepository.save(notification);

    // Notify through WebSocket
    this.notificationsGateway.sendToUser(userId, updatedNotification);

    return updatedNotification;
  }

  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationsRepository.update(
      { recipientId: userId, isRead: false },
      { isRead: true },
    );

    // Notify through WebSocket
    this.notificationsGateway.sendToUser(userId, {
      id: 'all',
      type: NotificationType.SYSTEM,
      title: 'All notifications marked as read',
      message: 'All your notifications have been marked as read',
      data: JSON.stringify({ userId }),
      isRead: true,
      recipientId: userId,
      priority: NotificationPriority.LOW,
      createdAt: new Date(),
      updatedAt: new Date(),
      recipient: null,
    } as unknown as Notification);
  }

  async delete(id: string, userId: string): Promise<void> {
    const result = await this.notificationsRepository.delete({ id, recipientId: userId });
    if (result.affected === 0) {
      throw new NotFoundException('Notification not found');
    }
  }

  async getUserPreferences(userId: string): Promise<NotificationPreference> {
    let preferences = await this.preferencesRepository.findOne({
      where: { userId },
    });

    if (!preferences) {
      // Create default preferences
      preferences = this.preferencesRepository.create({
        userId,
        emailPreferences: {
          [NotificationType.JOB_CREATED]: true,
          [NotificationType.JOB_UPDATED]: true,
          [NotificationType.JOB_APPLIED]: true,
          [NotificationType.APPLICATION_STATUS]: true,
          [NotificationType.SYSTEM]: true,
        },
        pushPreferences: {
          [NotificationType.JOB_CREATED]: true,
          [NotificationType.JOB_UPDATED]: true,
          [NotificationType.JOB_APPLIED]: true,
          [NotificationType.APPLICATION_STATUS]: true,
          [NotificationType.SYSTEM]: true,
        },
        inAppPreferences: {
          [NotificationType.JOB_CREATED]: true,
          [NotificationType.JOB_UPDATED]: true,
          [NotificationType.JOB_APPLIED]: true,
          [NotificationType.APPLICATION_STATUS]: true,
          [NotificationType.SYSTEM]: true,
        },
        receiveJobAlerts: true,
        receiveApplicationUpdates: true,
        receiveSystemNotifications: true,
        emailDigest: false,
        emailDigestFrequency: 'DAILY',
      });
      await this.preferencesRepository.save(preferences);
    }

    return preferences;
  }

  async updatePreferences(
    userId: string,
    updatePreferencesDto: Partial<NotificationPreference>,
  ): Promise<NotificationPreference> {
    const preferences = await this.getUserPreferences(userId);
    Object.assign(preferences, updatePreferencesDto);
    return this.preferencesRepository.save(preferences);
  }

  async notifyJobCreated(jobId: string, employerId: string): Promise<void> {
    const job = await this.jobsService.findOne(jobId, employerId);
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Get all users who might be interested in this job
    const { users } = await this.usersService.getUsers();
    
    // Create notifications for each user
    const notifications = users.map(user => ({
      userId: user.id,
      type: NotificationType.JOB_CREATED,
      data: {
        jobId: job.id,
        jobTitle: job.title,
        employerName: job.employer.firstName + ' ' + job.employer.lastName,
      },
    }));

    // Create notifications in parallel
    await Promise.all(
      notifications.map(notification => this.create(notification)),
    );
  }

  async notifyJobUpdated(jobId: string, employerId: string): Promise<void> {
    const job = await this.jobsService.findOne(jobId, employerId);
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Get all users who have applied to this job
    const applications = job.applications || [];
    
    // Create notifications for each applicant
    const notifications = applications.map(application => ({
      userId: application.applicant.id,
      type: NotificationType.JOB_UPDATED,
      data: {
        jobId: job.id,
        jobTitle: job.title,
        employerName: job.employer.firstName + ' ' + job.employer.lastName,
      },
    }));

    // Create notifications in parallel
    await Promise.all(
      notifications.map(notification => this.create(notification)),
    );
  }

  async notifyApplicationStatus(
    applicationId: string,
    status: string,
  ): Promise<void> {
    const job = await this.jobsService.findOne(applicationId, '');
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    const application = job.applications?.find(app => app.id === applicationId);
    if (!application) {
      throw new NotFoundException('Application not found');
    }

    await this.create({
      userId: application.applicant.id,
      type: NotificationType.APPLICATION_STATUS,
      data: {
        applicationId: application.id,
        jobId: job.id,
        jobTitle: job.title,
        status,
      },
    });
  }
} 