import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl } from 'class-validator';

export class CreateJobApplicationDto {
  @ApiProperty({
    description: 'Cover letter for the job application',
    required: false,
    example: 'I am excited to apply for this position...',
  })
  @IsString()
  @IsOptional()
  coverLetter?: string;

  @ApiProperty({
    description: "URL to the applicant's resume",
    required: false,
    example: 'https://example.com/resume.pdf',
  })
  @IsUrl()
  @IsOptional()
  resumeUrl?: string;
}
