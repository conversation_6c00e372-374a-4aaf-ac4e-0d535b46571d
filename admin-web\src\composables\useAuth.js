import { ref, computed } from 'vue'

// Initialize from localStorage with jdAdmin prefix
const getStoredAuth = () => {
  try {
    const stored = localStorage.getItem('jdAdmin-auth')
    return stored ? JSON.parse(stored) : { isAuthenticated: false, currentUser: null }
  } catch (error) {
    console.error('Error parsing stored auth data:', error)
    return { isAuthenticated: false, currentUser: null }
  }
}

const storedAuth = getStoredAuth()
const isAuthenticated = ref(storedAuth.isAuthenticated)
const currentUser = ref(storedAuth.currentUser)

// Save auth state to localStorage with jdAdmin prefix
const saveAuthState = () => {
  try {
    localStorage.setItem('jdAdmin-auth', JSON.stringify({
      isAuthenticated: isAuthenticated.value,
      currentUser: currentUser.value
    }))
  } catch (error) {
    console.error('Error saving auth state:', error)
  }
}

export function useAuth() {
  const login = async (credentials) => {
    // Simulate API call
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
          isAuthenticated.value = true
          currentUser.value = {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'Admin'
          }
          saveAuthState()
          resolve({ success: true })
        } else {
          reject({ message: 'Invalid credentials' })
        }
      }, 1000)
    })
  }

  const logout = () => {
    isAuthenticated.value = false
    currentUser.value = null
    saveAuthState()
  }

  return {
    isAuthenticated: computed(() => isAuthenticated.value),
    currentUser: computed(() => currentUser.value),
    login,
    logout
  }
}