import { createRouter, createWebHistory } from 'vue-router';
import { useAuth } from '../composables/useAuth';

// Lazy load components for better performance
const DashboardView = () => import('../views/DashboardView.vue');
const UsersView = () => import('../views/UsersView.vue');
const IndustriesView = () => import('../views/IndustriesView.vue');
const JobsView = () => import('../views/JobsView.vue');
const FeedbackView = () => import('../views/FeedbackView.vue');
const WelcomePageView = () => import('../views/WelcomePageView.vue');
const ErrorTrackingView = () => import('../views/ErrorTrackingView.vue');
const AnalyticsView = () => import('../views/AnalyticsView.vue');
const SettingsView = () => import('../views/SettingsView.vue');
const FilesView = () => import('../views/FilesView.vue');
const MessagesView = () => import('../views/MessagesView.vue');

const routes = [
  {
    path: '/',
    redirect: '/jdAdmin/dashboard',
  },
  {
    path: '/jdAdmin',
    redirect: '/jdAdmin/dashboard',
  },
  {
    path: '/jdAdmin/dashboard',
    name: 'Dashboard',
    component: DashboardView,
    meta: {
      requiresAuth: true,
      title: 'Dashboard',
      icon: 'pi pi-home',
    },
  },
  {
    path: '/jdAdmin/users',
    name: 'Users',
    component: UsersView,
    meta: {
      requiresAuth: true,
      title: 'Users',
      icon: 'pi pi-users',
    },
  },
  {
    path: '/jdAdmin/industries',
    name: 'Industries',
    component: IndustriesView,
    meta: {
      requiresAuth: true,
      title: 'Industries',
      icon: 'pi pi-building',
    },
  },
  {
    path: '/jdAdmin/jobs',
    name: 'Jobs',
    component: JobsView,
    meta: {
      requiresAuth: true,
      title: 'Jobs',
      icon: 'pi pi-briefcase',
    },
  },
  {
    path: '/jdAdmin/feedback',
    name: 'Feedback',
    component: FeedbackView,
    meta: {
      requiresAuth: true,
      title: 'Feedback',
      icon: 'pi pi-comments',
    },
  },
  {
    path: '/jdAdmin/welcome-page',
    name: 'WelcomePage',
    component: WelcomePageView,
    meta: {
      requiresAuth: true,
      title: 'Welcome Page',
      icon: 'pi pi-home',
    },
  },
  {
    path: '/jdAdmin/errors',
    name: 'ErrorTracking',
    component: ErrorTrackingView,
    meta: {
      requiresAuth: true,
      title: 'Error Tracking',
      icon: 'pi pi-exclamation-triangle',
    },
  },
  {
    path: '/jdAdmin/analytics',
    name: 'Analytics',
    component: AnalyticsView,
    meta: {
      requiresAuth: true,
      title: 'Analytics',
      icon: 'pi pi-chart-line',
    },
  },
  {
    path: '/jdAdmin/settings',
    name: 'Settings',
    component: SettingsView,
    meta: {
      requiresAuth: true,
      title: 'Settings',
      icon: 'pi pi-cog',
    },
  },
  {
    path: '/jdAdmin/files',
    name: 'Files',
    component: FilesView,
    meta: {
      requiresAuth: true,
      title: 'Files',
      icon: 'pi pi-folder',
    },
  },
  {
    path: '/jdAdmin/messages',
    name: 'Messages',
    component: MessagesView,
    meta: {
      requiresAuth: true,
      title: 'Messages',
      icon: 'pi pi-envelope',
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  const { isAuthenticated } = useAuth();
  next();
  // if (to.meta.requiresAuth && !isAuthenticated.value) {
  //   // Redirect to login if not authenticated
  //   next('/')
  // } else {
  //   next()
  // }
});

export default router;