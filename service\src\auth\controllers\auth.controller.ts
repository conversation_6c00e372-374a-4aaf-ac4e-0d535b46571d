import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  Get,
  HttpCode,
  HttpStatus,
  Res,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse as SwaggerResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { OtpService } from '../services/otp.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { LoginDto } from '../dto/login.dto';
import { EmailOtpLoginDto } from '../dto/email-otp-login.dto';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { ApiResponseDto } from '../../welcome/dto/api-response.dto';
import { Response } from 'express';
import { CustomHeaders } from '../../common/decorators/headers.decorator';
import { AuthGuard } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { LoginResponseDto } from '../dto/login-response.dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly otpService: OtpService,
    private readonly configService: ConfigService,
  ) {}

  private encryptState(state: any): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(
      'aes-256-cbc',
      Buffer.from(this.configService.get<string>('SESSION_SECRET'), 'hex'),
      iv
    );
    const encrypted = Buffer.concat([
      cipher.update(JSON.stringify(state)),
      cipher.final()
    ]);
    return iv.toString('hex') + encrypted.toString('hex');
  }

  @Post('login')
  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login with phone and password' })
  @ApiBody({ type: LoginDto })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Login successful',
    type: LoginResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async login(@Request() req, @CustomHeaders('usertype') usertype: string): Promise<LoginResponseDto> {
    try {
      const result = await this.authService.login(req.user, usertype);
      return result;
    } catch (error) {
      throw new Error('LOGIN_ERROR');
    }
  }

  @Post('login/email/otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request OTP for email login' })
  @ApiBody({ type: EmailOtpLoginDto })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'OTP sent successfully',
    type: ApiResponseDto,
  })
  async requestEmailOtp(@Body() emailOtpLoginDto: EmailOtpLoginDto, @CustomHeaders('jdu') userType: string): Promise<ApiResponseDto<any>> {
    try {
      await this.otpService.sendOTP(emailOtpLoginDto.email, userType);
      return ApiResponseDto.success({ message: 'OTP sent successfully' }, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('OTP_SEND_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Post('login/email/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify OTP and login' })
  @ApiBody({ type: VerifyOtpDto })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Login successful',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid OTP' })
  async verifyEmailOtp(@Body() verifyOtpDto: VerifyOtpDto, @Res({ passthrough: true }) response: Response, @CustomHeaders('jdu') userType: string): Promise<ApiResponseDto<any>> {
    try {
      const isValid = await this.otpService.verifyOTP(verifyOtpDto.email, verifyOtpDto.otp);

      if (!isValid) {
        return ApiResponseDto.error('INVALID_OTP', 'Invalid OTP', HttpStatus.UNAUTHORIZED);
      }

      const user = await this.otpService.findOrCreateUser(verifyOtpDto.email);
      const result = await this.authService.login(user);

      // Set cookies
      const cookieOptions = {
        httpOnly: true,
        secure: this.configService.get('NODE_ENV') === 'production',
        sameSite: 'strict' as const,
        maxAge: 15 * 60 * 1000, // 15 minutes for access token
      };

      const refreshCookieOptions = {
        ...cookieOptions,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days for refresh token
      };

      response.cookie(userType === 'EMPLOYER' ? 'eac.t' : 'jsac.t', result.access_token, cookieOptions);
      response.cookie(userType === 'EMPLOYER' ? 'erc.t' : 'jsrc.t', result.refresh_token, refreshCookieOptions);

      // Remove tokens from response body for security
      const { access_token, refresh_token, ...userData } = result;

      return ApiResponseDto.success(userData, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('OTP_VERIFY_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiBody({ type: RefreshTokenDto })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Token refresh successful',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid refresh token' })
  async refreshToken(
    @Body('refresh_token') refresh_token: string,
    @CustomHeaders(['usertype', 'deviceId']) headers: any,
  ): Promise<ApiResponseDto<any>> {
    try {
      const result = await this.authService.refreshToken(refresh_token, headers);
      return ApiResponseDto.success(result, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('TOKEN_REFRESH_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get current user profile' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Profile retrieved successfully',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async getProfile(@Request() req, @Res({ passthrough: true }) response: Response, @CustomHeaders('jdu') userType: string): Promise<ApiResponseDto<any>> {
    try {
      console.log(req.user)
      const result = await this.authService.login(req.user);

      // Set cookies
      const cookieOptions = {
        httpOnly: true,
        secure: this.configService.get('NODE_ENV') === 'production',
        sameSite: 'strict' as const,
        maxAge: 15 * 60 * 1000, // 15 minutes for access token
      };

      const refreshCookieOptions = {
        ...cookieOptions,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days for refresh token
      };

      response.cookie(userType === 'EMPLOYER' ? 'eac.t' : 'jsac.t', result.access_token, cookieOptions);
      response.cookie(userType === 'EMPLOYER' ? 'erc.t' : 'jsrc.t', result.refresh_token, refreshCookieOptions);

      // Remove tokens from response body for security
      const { access_token, refresh_token, ...userData } = result;

      return ApiResponseDto.success(userData, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('OTP_VERIFY_ERROR', error.message, HttpStatus.UNAUTHORIZED);
    }
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Logout user' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Logout successful',
    type: ApiResponseDto,
  })
  @SwaggerResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async logout(@Res({ passthrough: true }) response: Response, @CustomHeaders('jdu') userType: string): Promise<ApiResponseDto<any>> {
    // Clear cookies by setting them to expire immediately
    const cookieOptions = {
      httpOnly: true,
      secure: this.configService.get('NODE_ENV') === 'production',
      sameSite: 'strict' as const,
      expires: new Date(0), // Set to past date to expire immediately
      path: '/'
    };

    // Clear both access and refresh tokens
    response.clearCookie(userType === 'EMPLOYER' ? 'eac.t' : 'jsac.t', cookieOptions);
    response.clearCookie(userType === 'EMPLOYER' ? 'erc.t' : 'jsrc.t', cookieOptions);
    
    return ApiResponseDto.success({ message: 'Logout successful' }, HttpStatus.OK);
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Initiate Google OAuth2 login' })
  async googleAuth(@Req() req: any, @CustomHeaders(['usertype', 'deviceId']) headers: any) {
    console.log('Google Auth - Raw Query:', req.query);
    console.log('Google Auth - Raw Headers:', headers);
    // Get usertype from query or headers
    const usertype = req.query.usertype || req.query.usertype || headers?.usertype;
    console.log('Google Auth - Raw usertype:', usertype);
    if (usertype) {
      // Store usertype in session
      req.session.usertype = usertype.toUpperCase();
      console.log('Google Auth - Stored usertype in session:', req.session.usertype);
    }
    // The guard will handle the redirect
    return;
  }

  @Get('google/employer')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Initiate Google OAuth2 login for employers' })
  async googleAuthEmployer() {
    // This route is specifically for employers
    // The URL path will be used in the strategy to determine role
    console.log('Google Auth Employer - Route accessed');
    return; // Auth guard handles the redirect
  }

  @Get('google/jobseeker')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Initiate Google OAuth2 login for jobseekers' })
  async googleAuthJobseeker() {
    // This route is specifically for jobseekers
    // The URL path will be used in the strategy to determine role
    console.log('Google Auth Jobseeker - Route accessed');
    return; // Auth guard handles the redirect
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Handle Google OAuth2 callback' })
  async googleAuthCallback(@Req() req: any, @Res() res: Response) {
    try {
      console.log('Google Callback - User from auth guard:', req.user);
      
      // Check if this is an employer login
      const isEmployer = req.user.isEmployer === true;
      console.log('Google Callback - Is Employer:', isEmployer);
      
      // Pass the employer flag to the auth service
      const result = await this.authService.handleGoogleAuth(req.user, { isEmployer });
      
      // Redirect with tokens
      const redirectUrl = `${this.configService.get<string>(
        'FRONTEND_URL',
      )}/auth/google/callback?access_token=${result.access_token}&refresh_token=${
        result.refresh_token
      }`;
      
      return res.redirect(redirectUrl);
    } catch (error) {
      console.error('Google callback error:', error);
      return res.redirect(
        `${this.configService.get<string>('FRONTEND_URL')}/auth/login?error=google_auth_failed`,
      );
    }
  }
}
