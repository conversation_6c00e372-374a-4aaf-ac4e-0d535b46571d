import { Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class AppService {
  getAdmin(): string {
    try {
      // Read the Vue.js built index.html file
      const indexPath = join(__dirname, '..', 'public/admin/dist', 'index.html');
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      console.error('Error reading index.html:', error);
      return 'Vue.js application not found. Please build the frontend application first.';
    }
  }

  getEmployer(): string {
    try {
      const indexPath = join(__dirname, '..', 'public/employer/dist', 'index.html');
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      console.error('Error reading index.html:', error);
      return 'Application not found.';
    }
  }

  getJobseeker(): string {
    try {
      const indexPath = join(__dirname, '..', 'public/jobseeker/dist', 'index.html');
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      console.error('Error reading index.html:', error);
      return 'Application not found.';
    }
  }

  getWelcome(): string {
    try {
      const indexPath = join(__dirname, '..', 'public/welcome/dist', 'index.html');
      return readFileSync(indexPath, 'utf8');
    } catch (error) {
      console.error('Error reading index.html:', error);
      return 'Application not found.';
    }
  }
}
