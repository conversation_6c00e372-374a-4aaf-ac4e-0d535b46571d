<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'

const sidebarOpen = ref(false)
const sidebarCollapsed = ref(false)
const windowWidth = ref(window.innerWidth)

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const handleSidebarToggle = (collapsed) => {
  sidebarCollapsed.value = collapsed
}

// Handle window resize
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// Compute main content margin based on sidebar state
const mainContentMargin = computed(() => {
  if (windowWidth.value <= 768) {
    return '0'
  }
  return sidebarCollapsed.value ? '70px' : '240px'
})

// Compute header left position based on sidebar state
const headerLeftPosition = computed(() => {
  if (windowWidth.value <= 768) {
    return '0'
  }
  return sidebarCollapsed.value ? '70px' : '240px'
})
</script>

<template>
  <div class="admin-layout">
    <Sidebar 
      :class="{ open: sidebarOpen }"
      @sidebar-toggle="handleSidebarToggle"
    />
    
    <div class="main-layout">
     <!-- <Header 
        @toggle-sidebar="toggleSidebar" 
        :class="{ 'sidebar-collapsed': sidebarCollapsed }"
        :style="{ left: headerLeftPosition }"
      /> -->
      <main class="main-content" :style="{ marginLeft: mainContentMargin }">
        <div class="content-container">
          <router-view />
        </div>
      </main>
    </div>
    
    <div 
      v-if="sidebarOpen" 
      class="sidebar-overlay"
      @click="toggleSidebar"
    ></div>
  </div>
</template>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: var(--p-surface-ground);
}

.main-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-content {
  min-height: calc(100vh - 60px);
  background: var(--p-surface-ground);
  transition: margin-left 0.3s ease;
}

.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important;
  }
  
  .sidebar-overlay {
    display: block;
  }
}
</style>