import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WelcomeController } from './controllers/welcome.controller';
import { WelcomeService } from './services/welcome.service';
import { WelcomeEntity } from './entities/welcome.entity';
import { DescriptionEntity } from './entities/description.entity';
import { AudienceMessageEntity } from './entities/audience-message.entity';
import { TestimonialEntity } from './entities/testimonial.entity';
import { DescriptionController } from './controllers/description.controller';
import { DescriptionService } from './services/description.service';
import { AudienceMessageController } from './controllers/audience-message.controller';
import { AudienceMessageService } from './services/audience-message.service';
import { TestimonialController } from './controllers/testimonial.controller';
import { TestimonialService } from './services/testimonial.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WelcomeEntity,
      DescriptionEntity,
      AudienceMessageEntity,
      TestimonialEntity,
    ]),
  ],
  controllers: [
    Welcome<PERSON><PERSON>roller,
    Description<PERSON>ontroller,
    AudienceMessageController,
    TestimonialController,
  ],
  providers: [WelcomeService, DescriptionService, AudienceMessageService, TestimonialService],
  exports: [WelcomeService, DescriptionService, AudienceMessageService, TestimonialService],
})
export class WelcomeModule implements OnModuleInit {
  constructor(private readonly welcomeService: WelcomeService) {}

  async onModuleInit() {
    // Create default welcome data if none exists
    await this.welcomeService.getWelcomeData();
  }
}
