import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WelcomeEntity } from '../entities/welcome.entity';
import { DescriptionEntity } from '../entities/description.entity';
import { AudienceMessageEntity } from '../entities/audience-message.entity';
import { TestimonialEntity } from '../entities/testimonial.entity';
import { CreateWelcomeDto } from '../dto/create-welcome.dto';
import { UpdateWelcomeDto } from '../dto/update-welcome.dto';
import { UpdateTestimonialDto } from '../dto/testimonial.dto';

@Injectable()
export class WelcomeService {
  private readonly SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000';

  constructor(
    @InjectRepository(WelcomeEntity)
    private readonly welcomeRepository: Repository<WelcomeEntity>,
    @InjectRepository(DescriptionEntity)
    private readonly descriptionRepository: Repository<DescriptionEntity>,
    @InjectRepository(AudienceMessageEntity)
    private readonly audienceMessageRepository: Repository<AudienceMessageEntity>,
    @InjectRepository(TestimonialEntity)
    private readonly testimonialRepository: Repository<TestimonialEntity>,
  ) {}

  async create(adminId: string, createWelcomeDto: CreateWelcomeDto): Promise<WelcomeEntity> {
    const welcome = this.welcomeRepository.create({
      ...createWelcomeDto,
      updatedBy: adminId,
    });
    return this.welcomeRepository.save(welcome);
  }

  async findOne(): Promise<WelcomeEntity> {
    const welcome = await this.welcomeRepository.findOne({
      where: {}, // Empty where clause to get any record
      order: { createdAt: 'DESC' }, // Get the most recent one
      relations: ['descriptions', 'audience_messages', 'testimonials'],
    });

    if (!welcome) {
      return this.createDefaultWelcome();
    }

    return welcome;
  }

  async update(adminId: string, updateWelcomeDto: UpdateWelcomeDto): Promise<WelcomeEntity> {
    const welcome = await this.findOne();

    Object.assign(welcome, {
      ...updateWelcomeDto,
      updatedBy: adminId,
    });

    return this.welcomeRepository.save(welcome);
  }

  async createDefaultWelcome(): Promise<WelcomeEntity> {
    const defaultWelcome = this.welcomeRepository.create({
      cmp_name: 'Blu-Collar',
      logo_path: '/assets/images/logo.png',
      show_signup: true,
      show_login: true,
      welcome_pop_msg: 'Welcome to Blu-Collar',
      base_url: 'http://localhost:3000',
      notification_url: 'http://localhost:3000/notifications',
      user_url: 'http://localhost:3000/users',
      message: 'Welcome to Blu-Collar - Your trusted platform for professional services',
      imageUrl: '/assets/images/welcome-banner.png',
      updatedBy: this.SYSTEM_USER_ID,
    });

    const savedWelcome = await this.welcomeRepository.save(defaultWelcome);

    // Create default description
    const defaultDescription = this.descriptionRepository.create({
      description: 'Your trusted platform for professional services',
      welcome: savedWelcome,
    });
    await this.descriptionRepository.save(defaultDescription);

    // Create default audience message
    const defaultMessage = this.audienceMessageRepository.create({
      message: 'Join our community of professionals',
      welcome: savedWelcome,
    });
    await this.audienceMessageRepository.save(defaultMessage);

    // Create default testimonial
    const defaultTestimonial = {
      content: 'This platform has transformed how I find work opportunities',
      authorName: 'Anonymous',
      authorRole: 'User',
      userId: this.SYSTEM_USER_ID,
      isApproved: true,
      welcome: savedWelcome,
    };

    try {
      await this.testimonialRepository.save(defaultTestimonial);
    } catch (error) {
      console.error('Error creating default testimonial:', error);
      // Continue even if testimonial creation fails
    }

    return this.welcomeRepository.findOne({
      where: { id: savedWelcome.id },
      relations: ['descriptions', 'audience_messages', 'testimonials'],
    });
  }

  async getWelcomeData(): Promise<WelcomeEntity> {
    // First try to find the most recent welcome data
    const welcomeData = await this.welcomeRepository.findOne({
      where: {}, // Empty where clause to get any record
      order: { createdAt: 'DESC' }, // Get the most recent one
      relations: ['descriptions', 'audience_messages', 'testimonials'],
    });

    if (!welcomeData) {
      return this.createDefaultWelcome();
    }

    // Filter out unapproved testimonials
    welcomeData.testimonials = welcomeData.testimonials.filter(
      (testimonial) => testimonial.isApproved,
    );

    return welcomeData;
  }

  async updateWelcomeData(id: string, updateDto: UpdateWelcomeDto): Promise<WelcomeEntity> {
    const welcome = await this.welcomeRepository.findOne({
      where: { id },
      relations: ['descriptions', 'audience_messages', 'testimonials'],
    });

    if (!welcome) {
      throw new Error('Welcome data not found');
    }

    if (updateDto.message) {
      welcome.message = updateDto.message;
    }

    if (updateDto.imageUrl) {
      welcome.imageUrl = updateDto.imageUrl;
    }

    if (updateDto.updatedBy) {
      welcome.updatedBy = updateDto.updatedBy;
    }

    if (updateDto.app_version) {
      welcome.app_version = updateDto.app_version;
    }

    if (updateDto.app_update_message) {
      welcome.app_update_message = updateDto.app_update_message;
    }

    if (updateDto.cmp_name) {
      welcome.cmp_name = updateDto.cmp_name;
    }

    if (updateDto.logo_path) {
      welcome.logo_path = updateDto.logo_path;
    }

    if (updateDto.welcome_pop_msg) {
      welcome.welcome_pop_msg = updateDto.welcome_pop_msg;
    }

    if (updateDto.base_url) {
      welcome.base_url = updateDto.base_url;
    }

    if (updateDto.notification_url) {
      welcome.notification_url = updateDto.notification_url;
    }

    if (updateDto.user_url) {
      welcome.user_url = updateDto.user_url;
    }

    if (updateDto.testimonials) {
      try {
        // Delete existing testimonials
        await this.testimonialRepository.delete({ welcome: { id } });

        // Create new testimonials with required fields
        const testimonials = updateDto.testimonials.map((test) => ({
          content: test.content,
          authorName: test.authorName || 'Anonymous',
          authorRole: test.authorRole || 'User',
          userId: this.SYSTEM_USER_ID,
          isApproved: test.isApproved ?? false,
          welcome,
        }));

        await this.testimonialRepository.save(testimonials);
      } catch (error) {
        console.error('Error updating testimonials:', error);
        throw new Error('Failed to update testimonials: ' + error.message);
      }
    }

    const updatedWelcome = await this.welcomeRepository.save(welcome);
    const welcomeWithRelations = await this.welcomeRepository.findOne({
      where: { id: updatedWelcome.id },
      relations: ['descriptions', 'audience_messages', 'testimonials'],
    });

    // Filter out unapproved testimonials
    welcomeWithRelations.testimonials = welcomeWithRelations.testimonials.filter(
      (testimonial) => testimonial.isApproved,
    );

    return welcomeWithRelations;
  }
}
