<template>
  <AppLayout>
    <div class="notifications-page">
      <div class="notifications-content">
        <!-- <PERSON> Header -->
        <div class="page-header">
          <div class="header-content">
            <div class="header-left">
              <i class="pi pi-bell header-icon"></i>
              <div class="header-text">
                <h1>Notifications</h1>
                <p>Stay updated with your job search activities</p>
              </div>
            </div>
            <div class="header-actions">
              <Button 
                @click="refreshNotifications"
                :loading="isLoading"
                icon="pi pi-refresh"
                severity="secondary"
                outlined
                v-tooltip="'Refresh Notifications'"
              />
              <Button 
                @click="markAllAsRead"
                icon="pi pi-check"
                severity="success"
                outlined
                :disabled="unreadCount === 0"
                v-tooltip="'Mark All as Read'"
              />
              <Button 
                @click="showSettings = true"
                icon="pi pi-cog"
                severity="secondary"
                outlined
                v-tooltip="'Notification Settings'"
              />
            </div>
          </div>
        </div>

        <!-- Notifications Stats -->
        <div class="stats-overview">
          <div class="stat-card total">
            <div class="stat-icon">
              <i class="pi pi-bell"></i>
            </div>
            <div class="stat-content">
              <h3>{{ notifications.length }}</h3>
              <p>Total Notifications</p>
            </div>
          </div>
          <div class="stat-card unread">
            <div class="stat-icon">
              <i class="pi pi-circle-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ unreadCount }}</h3>
              <p>Unread</p>
            </div>
          </div>
          <div class="stat-card today">
            <div class="stat-icon">
              <i class="pi pi-calendar"></i>
            </div>
            <div class="stat-content">
              <h3>{{ todayNotifications.length }}</h3>
              <p>Today</p>
            </div>
          </div>
          <div class="stat-card important">
            <div class="stat-icon">
              <i class="pi pi-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
              <h3>{{ importantNotifications.length }}</h3>
              <p>Important</p>
            </div>
          </div>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
          <Button 
            @click="activeFilter = 'all'"
            :class="['filter-tab', { active: activeFilter === 'all' }]"
            text
          >
            All ({{ notifications.length }})
          </Button>
          <Button 
            @click="activeFilter = 'unread'"
            :class="['filter-tab', { active: activeFilter === 'unread' }]"
            text
          >
            Unread ({{ unreadCount }})
          </Button>
          <Button 
            @click="activeFilter = 'job_alerts'"
            :class="['filter-tab', { active: activeFilter === 'job_alerts' }]"
            text
          >
            Job Alerts ({{ jobAlertNotifications.length }})
          </Button>
          <Button 
            @click="activeFilter = 'applications'"
            :class="['filter-tab', { active: activeFilter === 'applications' }]"
            text
          >
            Applications ({{ applicationNotifications.length }})
          </Button>
          <Button 
            @click="activeFilter = 'system'"
            :class="['filter-tab', { active: activeFilter === 'system' }]"
            text
          >
            System ({{ systemNotifications.length }})
          </Button>
        </div>

        <!-- Notifications List -->
        <div class="notifications-list" v-if="filteredNotifications.length > 0">
          <div 
            v-for="notification in filteredNotifications" 
            :key="notification.id"
            :class="['notification-card', { 
              'unread': !notification.isRead, 
              'important': notification.priority === 'high',
              'clickable': notification.actionUrl
            }]"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-header">
              <div class="notification-icon">
                <i :class="getNotificationIcon(notification.type)" :style="{ color: getNotificationColor(notification.type) }"></i>
              </div>
              <div class="notification-content">
                <div class="notification-title-row">
                  <h4 class="notification-title">{{ notification.title }}</h4>
                  <div class="notification-meta">
                    <Tag 
                      v-if="notification.priority === 'high'"
                      value="Important" 
                      severity="danger"
                      class="priority-tag"
                    />
                    <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
                  </div>
                </div>
                <p class="notification-message">{{ notification.message }}</p>
                <div class="notification-details" v-if="notification.details">
                  <div class="detail-item" v-for="(value, key) in notification.details" :key="key">
                    <strong>{{ formatDetailKey(key) }}:</strong> {{ value }}
                  </div>
                </div>
              </div>
              <div class="notification-actions">
                <Button 
                  v-if="!notification.isRead"
                  @click.stop="markAsRead(notification)"
                  icon="pi pi-check"
                  text
                  size="small"
                  v-tooltip="'Mark as Read'"
                  class="mark-read-btn"
                />
                <Button 
                  @click.stop="deleteNotification(notification)"
                  icon="pi pi-trash"
                  text
                  size="small"
                  severity="danger"
                  v-tooltip="'Delete'"
                  class="delete-btn"
                />
              </div>
            </div>
            
            <!-- Action Buttons for specific notification types -->
            <div class="notification-footer" v-if="hasActionButtons(notification)">
              <div class="action-buttons">
                <Button 
                  v-if="notification.type === 'job_match'"
                  @click.stop="viewJob(notification.jobId)"
                  icon="pi pi-eye"
                  label="View Job"
                  size="small"
                  outlined
                />
                <Button 
                  v-if="notification.type === 'application_update'"
                  @click.stop="viewApplication(notification.applicationId)"
                  icon="pi pi-send"
                  label="View Application"
                  size="small"
                  outlined
                />
                <Button 
                  v-if="notification.type === 'interview_scheduled'"
                  @click.stop="viewInterview(notification.interviewId)"
                  icon="pi pi-calendar"
                  label="View Interview"
                  size="small"
                  outlined
                />
                <Button 
                  v-if="notification.actionUrl"
                  @click.stop="openActionUrl(notification.actionUrl)"
                  icon="pi pi-external-link"
                  label="Take Action"
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading" class="empty-state">
          <div class="empty-content">
            <i class="pi pi-bell empty-icon"></i>
            <h3>{{ getEmptyStateTitle() }}</h3>
            <p>{{ getEmptyStateMessage() }}</p>
            <div class="empty-actions" v-if="activeFilter === 'all'">
              <Button 
                @click="enableNotifications"
                icon="pi pi-bell"
                label="Enable Notifications"
                outlined
              />
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="loading-state">
          <i class="pi pi-spin pi-spinner loading-icon"></i>
          <p>Loading notifications...</p>
        </div>
      </div>
    </div>

    <!-- Notification Settings Dialog -->
    <Dialog 
      v-model:visible="showSettings" 
      header="Notification Settings"
      :modal="true"
      :closable="true"
      class="settings-dialog"
      :style="{ width: '500px' }"
    >
      <div class="settings-content">
        <div class="setting-section">
          <h4>Email Notifications</h4>
          <div class="setting-item">
            <div class="setting-info">
              <label>Job Alerts</label>
              <p>Receive emails when new jobs match your preferences</p>
            </div>
            <ToggleButton v-model="settings.emailJobAlerts" />
          </div>
          <div class="setting-item">
            <div class="setting-info">
              <label>Application Updates</label>
              <p>Get notified about application status changes</p>
            </div>
            <ToggleButton v-model="settings.emailApplicationUpdates" />
          </div>
          <div class="setting-item">
            <div class="setting-info">
              <label>Interview Reminders</label>
              <p>Receive reminders about upcoming interviews</p>
            </div>
            <ToggleButton v-model="settings.emailInterviewReminders" />
          </div>
        </div>

        <div class="setting-section">
          <h4>Push Notifications</h4>
          <div class="setting-item">
            <div class="setting-info">
              <label>Browser Notifications</label>
              <p>Show notifications in your browser</p>
            </div>
            <ToggleButton v-model="settings.browserNotifications" />
          </div>
          <div class="setting-item">
            <div class="setting-info">
              <label>Sound Alerts</label>
              <p>Play sound for important notifications</p>
            </div>
            <ToggleButton v-model="settings.soundAlerts" />
          </div>
        </div>

        <div class="setting-section">
          <h4>Frequency</h4>
          <div class="setting-item">
            <div class="setting-info">
              <label>Job Alert Frequency</label>
              <p>How often to receive job alerts</p>
            </div>
            <Dropdown
              v-model="settings.jobAlertFrequency"
              :options="frequencyOptions"
              optionLabel="label"
              optionValue="value"
              class="frequency-dropdown"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="settings-footer">
          <Button 
            @click="showSettings = false"
            label="Cancel"
            outlined
          />
          <Button 
            @click="saveSettings"
            label="Save Settings"
            class="save-btn"
          />
        </div>
      </template>
    </Dialog>
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Button from 'primevue/button'
import Tag from 'primevue/tag'
import Dialog from 'primevue/dialog'
import ToggleButton from 'primevue/togglebutton'
import Dropdown from 'primevue/dropdown'
import AppLayout from '@/components/AppLayout.vue'
import alertManager from '@/utils/alertManager'

const router = useRouter()
const { t } = useI18n()

// State
const isLoading = ref(false)
const activeFilter = ref('all')
const showSettings = ref(false)

// Notification settings
const settings = ref({
  emailJobAlerts: true,
  emailApplicationUpdates: true,
  emailInterviewReminders: true,
  browserNotifications: true,
  soundAlerts: false,
  jobAlertFrequency: 'daily'
})

const frequencyOptions = [
  { label: 'Immediately', value: 'immediate' },
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Never', value: 'never' }
]

// Mock notifications data
const notifications = ref([
  {
    id: 1,
    type: 'job_match',
    title: 'New Job Match Found',
    message: 'A new Construction Worker position at ABC Construction matches your preferences.',
    details: {
      company: 'ABC Construction',
      location: 'Mumbai, Maharashtra',
      salary: '₹25,000 - ₹35,000'
    },
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    jobId: 'job-123',
    actionUrl: '/employer/jobs/job-123'
  },
  {
    id: 2,
    type: 'application_update',
    title: 'Application Status Updated',
    message: 'Your application for Warehouse Associate at XYZ Logistics has been reviewed.',
    details: {
      jobTitle: 'Warehouse Associate',
      company: 'XYZ Logistics',
      status: 'Under Review'
    },
    priority: 'medium',
    isRead: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    applicationId: 'app-456'
  },
  {
    id: 3,
    type: 'interview_scheduled',
    title: 'Interview Scheduled',
    message: 'Your interview for Security Guard position has been scheduled.',
    details: {
      jobTitle: 'Security Guard',
      company: 'SecureTech Services',
      date: 'Tomorrow, 2:00 PM',
      type: 'Phone Interview'
    },
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    interviewId: 'int-789'
  },
  {
    id: 4,
    type: 'job_alert',
    title: 'Weekly Job Alert',
    message: '15 new jobs matching your preferences have been posted this week.',
    details: {
      newJobs: 15,
      topMatch: 'Delivery Driver at FastDelivery Inc.'
    },
    priority: 'medium',
    isRead: true,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 5,
    type: 'profile_view',
    title: 'Profile Viewed',
    message: 'An employer viewed your profile.',
    details: {
      company: 'Industrial Solutions',
      viewedAt: '2 hours ago'
    },
    priority: 'low',
    isRead: true,
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 6,
    type: 'system',
    title: 'Profile Completion Reminder',
    message: 'Complete your profile to increase your chances of getting hired.',
    details: {
      completion: '75%',
      missingItems: 'Skills, Work Experience'
    },
    priority: 'medium',
    isRead: true,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    actionUrl: '/employer/profile'
  },
  {
    id: 7,
    type: 'job_recommendation',
    title: 'Job Recommendations',
    message: 'We found 5 new jobs that might interest you based on your profile.',
    details: {
      count: 5,
      topRecommendation: 'Maintenance Technician'
    },
    priority: 'medium',
    isRead: true,
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 8,
    type: 'application_reminder',
    title: 'Application Deadline Reminder',
    message: 'The application deadline for Construction Worker at ABC Construction is tomorrow.',
    details: {
      jobTitle: 'Construction Worker',
      company: 'ABC Construction',
      deadline: 'Tomorrow'
    },
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
    jobId: 'job-123'
  }
])

// Computed properties
const unreadCount = computed(() => 
  notifications.value.filter(n => !n.isRead).length
)

const todayNotifications = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return notifications.value.filter(n => new Date(n.createdAt) >= today)
})

const importantNotifications = computed(() => 
  notifications.value.filter(n => n.priority === 'high')
)

const jobAlertNotifications = computed(() => 
  notifications.value.filter(n => ['job_match', 'job_alert', 'job_recommendation'].includes(n.type))
)

const applicationNotifications = computed(() => 
  notifications.value.filter(n => ['application_update', 'interview_scheduled', 'application_reminder'].includes(n.type))
)

const systemNotifications = computed(() => 
  notifications.value.filter(n => ['system', 'profile_view'].includes(n.type))
)

const filteredNotifications = computed(() => {
  switch (activeFilter.value) {
    case 'unread':
      return notifications.value.filter(n => !n.isRead)
    case 'job_alerts':
      return jobAlertNotifications.value
    case 'applications':
      return applicationNotifications.value
    case 'system':
      return systemNotifications.value
    default:
      return notifications.value
  }
})

// Helper functions
const getNotificationIcon = (type) => {
  const iconMap = {
    job_match: 'pi pi-briefcase',
    application_update: 'pi pi-send',
    interview_scheduled: 'pi pi-calendar',
    job_alert: 'pi pi-bell',
    profile_view: 'pi pi-eye',
    system: 'pi pi-cog',
    job_recommendation: 'pi pi-star',
    application_reminder: 'pi pi-clock'
  }
  return iconMap[type] || 'pi pi-info-circle'
}

const getNotificationColor = (type) => {
  const colorMap = {
    job_match: '#3b82f6',
    application_update: '#10b981',
    interview_scheduled: '#f59e0b',
    job_alert: '#8b5cf6',
    profile_view: '#06b6d4',
    system: '#6b7280',
    job_recommendation: '#ec4899',
    application_reminder: '#ef4444'
  }
  return colorMap[type] || '#6b7280'
}

const formatTime = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMinutes = Math.floor((now - date) / (1000 * 60))
  
  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  
  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return `${diffHours}h ago`
  
  const diffDays = Math.floor(diffHours / 24)
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}

const formatDetailKey = (key) => {
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
}

const hasActionButtons = (notification) => {
  return notification.type === 'job_match' || 
         notification.type === 'application_update' || 
         notification.type === 'interview_scheduled' ||
         notification.actionUrl
}

const getEmptyStateTitle = () => {
  switch (activeFilter.value) {
    case 'unread':
      return 'No unread notifications'
    case 'job_alerts':
      return 'No job alerts'
    case 'applications':
      return 'No application notifications'
    case 'system':
      return 'No system notifications'
    default:
      return 'No notifications yet'
  }
}

const getEmptyStateMessage = () => {
  switch (activeFilter.value) {
    case 'unread':
      return 'All your notifications have been read.'
    case 'job_alerts':
      return 'You\'ll receive notifications when new jobs match your preferences.'
    case 'applications':
      return 'Application updates and interview notifications will appear here.'
    case 'system':
      return 'System notifications and profile updates will appear here.'
    default:
      return 'Stay tuned for updates about your job search activities.'
  }
}

// Actions
const refreshNotifications = () => {
  isLoading.value = true
  // Simulate API call
  setTimeout(() => {
    isLoading.value = false
    alertManager.showSuccess('Refreshed', 'Notifications have been refreshed')
  }, 1000)
}

const markAllAsRead = async () => {
  const confirmed = await alertManager.showConfirm(
    'Mark All as Read',
    'Are you sure you want to mark all notifications as read?',
    {
      confirmText: 'Mark All',
      cancelText: 'Cancel'
    }
  )
  
  if (confirmed) {
    notifications.value.forEach(n => n.isRead = true)
    alertManager.showSuccess('Success', 'All notifications marked as read')
  }
}

const markAsRead = (notification) => {
  notification.isRead = true
}

const deleteNotification = async (notification) => {
  const confirmed = await alertManager.showConfirm(
    'Delete Notification',
    'Are you sure you want to delete this notification?',
    {
      confirmText: 'Delete',
      cancelText: 'Cancel'
    }
  )
  
  if (confirmed) {
    const index = notifications.value.findIndex(n => n.id === notification.id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
      alertManager.showSuccess('Deleted', 'Notification deleted')
    }
  }
}

const handleNotificationClick = (notification) => {
  if (!notification.isRead) {
    markAsRead(notification)
  }
  
  if (notification.actionUrl) {
    router.push(notification.actionUrl)
  }
}

const viewJob = (jobId) => {
  router.push(`/employer/jobs/${jobId}`)
}

const viewApplication = (applicationId) => {
  router.push('/employer/applications')
}

const viewInterview = (interviewId) => {
  // TODO: Navigate to interview details
  console.log('View interview:', interviewId)
}

const openActionUrl = (url) => {
  router.push(url)
}

const enableNotifications = () => {
  showSettings.value = true
}

const saveSettings = () => {
  // TODO: Save settings to backend
  showSettings.value = false
  alertManager.showSuccess('Settings Saved', 'Your notification preferences have been updated')
}

onMounted(() => {
  // Load notifications data
  // Request browser notification permission if needed
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
})
</script>

<style scoped>
.notifications-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--jobs-bg-start) 0%, var(--jobs-bg-end) 100%);
}

.notifications-content {
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.header-text h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.header-text p {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-duration) ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--highlight-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.stat-card.unread .stat-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--red-500);
}

.stat-card.today .stat-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--green-500);
}

.stat-card.important .stat-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--yellow-500);
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1.75rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--surface-border);
  overflow-x: auto;
}

.filter-tab {
  padding: 1rem 1.5rem !important;
  border-bottom: 2px solid transparent !important;
  color: var(--text-color-secondary) !important;
  font-weight: 500 !important;
  white-space: nowrap;
  transition: all var(--transition-duration) ease !important;
}

.filter-tab:hover {
  color: var(--primary-color) !important;
  background: var(--surface-hover) !important;
}

.filter-tab.active {
  color: var(--primary-color) !important;
  border-bottom-color: var(--primary-color) !important;
  background: var(--highlight-bg) !important;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-card {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all var(--transition-duration) ease;
  position: relative;
}

.notification-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-card.unread {
  border-left: 4px solid var(--primary-color);
  background: linear-gradient(135deg, var(--surface-card) 0%, var(--highlight-bg) 100%);
}

.notification-card.important {
  border-left: 4px solid var(--red-500);
}

.notification-card.clickable {
  cursor: pointer;
}

.notification-card.clickable:hover {
  border-color: var(--primary-color);
}

.notification-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--surface-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.notification-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.priority-tag {
  font-size: 0.7rem !important;
}

.notification-time {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  white-space: nowrap;
}

.notification-message {
  color: var(--text-color-secondary);
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.notification-details {
  background: var(--surface-50);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.detail-item {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item strong {
  color: var(--text-color);
}

.notification-actions {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
}

.mark-read-btn {
  color: var(--green-500) !important;
}

.delete-btn {
  color: var(--red-500) !important;
}

.notification-footer {
  border-top: 1px solid var(--surface-border);
  padding-top: 1rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-content {
  max-width: 400px;
}

.empty-icon,
.loading-icon {
  font-size: 4rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.empty-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Settings Dialog Styles */
.settings-dialog {
  border-radius: 12px !important;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.setting-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--surface-border);
  padding-bottom: 0.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-info {
  flex: 1;
}

.setting-info label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
  display: block;
  margin-bottom: 0.25rem;
}

.setting-info p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
}

.frequency-dropdown {
  min-width: 150px;
}

.settings-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

@media (max-width: 768px) {
  .notifications-content {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions {
    align-self: flex-end;
  }

  .header-text h1 {
    font-size: 2rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-tabs {
    gap: 0.25rem;
  }

  .filter-tab {
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  .notification-header {
    flex-direction: column;
    gap: 1rem;
  }

  .notification-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .notification-meta {
    align-self: flex-start;
  }

  .notification-actions {
    align-self: flex-end;
  }

  .action-buttons {
    flex-direction: column;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .settings-footer {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    gap: 0.5rem;
  }
}
</style>