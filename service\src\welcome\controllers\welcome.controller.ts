import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  HttpStatus,
  Request,
} from '@nestjs/common';
import { WelcomeService } from '../services/welcome.service';
import { CreateWelcomeDto } from '../dto/create-welcome.dto';
import { UpdateWelcomeDto } from '../dto/update-welcome.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/enums/user.enum';
import { ApiTags, ApiOperation, ApiResponse as SwaggerResponse } from '@nestjs/swagger';
import { ApiResponseDto } from '../dto/api-response.dto';
import { WelcomeEntity } from '../entities/welcome.entity';

@ApiTags('Welcome')
@Controller('welcome')
export class WelcomeController {
  constructor(private readonly welcomeService: WelcomeService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Create a new welcome message' })
  @SwaggerResponse({
    status: HttpStatus.CREATED,
    description: 'Welcome message created successfully',
    type: ApiResponseDto<WelcomeEntity>,
  })
  async create(
    @Request() req,
    @Body() createWelcomeDto: CreateWelcomeDto,
  ): Promise<ApiResponseDto<WelcomeEntity>> {
    try {
      const welcome = await this.welcomeService.create(req.user.id, createWelcomeDto);
      return ApiResponseDto.success(welcome, HttpStatus.CREATED);
    } catch (error) {
      return ApiResponseDto.error(
        'WELCOME_CREATE_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get the current welcome message' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Welcome message retrieved successfully',
    type: ApiResponseDto<WelcomeEntity>,
  })
  async findOne(): Promise<ApiResponseDto<WelcomeEntity>> {
    try {
      const welcome = await this.welcomeService.findOne();
      return ApiResponseDto.success(welcome, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('WELCOME_NOT_FOUND', error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update a welcome message' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Welcome message updated successfully',
    type: ApiResponseDto<WelcomeEntity>,
  })
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateWelcomeDto: UpdateWelcomeDto,
  ): Promise<ApiResponseDto<WelcomeEntity>> {
    try {
      const welcome = await this.welcomeService.update(req.user.id, updateWelcomeDto);
      return ApiResponseDto.success(welcome, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error('WELCOME_UPDATE_ERROR', error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('data')
  @ApiOperation({ summary: 'Get welcome data with all related information' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Welcome data retrieved successfully',
    type: ApiResponseDto<WelcomeEntity>,
  })
  async getWelcomeData(): Promise<ApiResponseDto<WelcomeEntity>> {
    try {
      const welcomeData = await this.welcomeService.getWelcomeData();
      return ApiResponseDto.success(welcomeData, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'WELCOME_DATA_ERROR',
        error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('data/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({ summary: 'Update welcome data with all related information' })
  @SwaggerResponse({
    status: HttpStatus.OK,
    description: 'Welcome data updated successfully',
    type: ApiResponseDto<WelcomeEntity>,
  })
  async updateWelcomeData(
    @Param('id') id: string,
    @Body() updateDto: UpdateWelcomeDto,
  ): Promise<ApiResponseDto<WelcomeEntity>> {
    try {
      const welcomeData = await this.welcomeService.updateWelcomeData(id, updateDto);
      return ApiResponseDto.success(welcomeData, HttpStatus.OK);
    } catch (error) {
      return ApiResponseDto.error(
        'WELCOME_DATA_UPDATE_ERROR',
        error.message,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
