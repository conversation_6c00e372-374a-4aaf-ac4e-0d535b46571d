<script setup>
import { ref, reactive, computed } from 'vue'
import Card from 'primevue/card'
import But<PERSON> from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dialog from 'primevue/dialog'
import Dropdown from 'primevue/dropdown'
import Tag from 'primevue/tag'
import Calendar from 'primevue/calendar'
import MultiSelect from 'primevue/multiselect'
import ConfirmDialog from 'primevue/confirmdialog'
import { useConfirm } from 'primevue/useconfirm'

const confirm = useConfirm()

// Feedback types enum based on the DTO
const FeedbackType = {
  SUGGESTION: 'SUGGESTION',
  BUG_REPORT: 'BUG_REPORT',
  FEATURE_REQUEST: 'FEATURE_REQUEST',
  COMPLAINT: 'COMPLAINT',
  COMPLIMENT: 'COMPLIMENT',
  GENERAL: 'GENERAL'
}

// Sample feedback data
const feedbacks = ref([
  {
    id: '1',
    type: FeedbackType.SUGGESTION,
    title: 'Add dark mode support',
    description: 'It would be great to have a dark mode option for better visibility in low-light conditions.',
    metadata: { 
      screenshots: ['url1', 'url2'],
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      priority: 'medium'
    },
    status: 'open',
    submittedBy: '<EMAIL>',
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-15T10:30:00Z')
  },
  {
    id: '2',
    type: FeedbackType.BUG_REPORT,
    title: 'Login form validation error',
    description: 'The login form shows an error message even when valid credentials are entered.',
    metadata: { 
      steps: ['Navigate to login', 'Enter valid credentials', 'Click submit'],
      browser: 'Chrome 120',
      priority: 'high'
    },
    status: 'in_progress',
    submittedBy: '<EMAIL>',
    createdAt: new Date('2024-01-14T16:45:00Z'),
    updatedAt: new Date('2024-01-16T09:20:00Z')
  },
  {
    id: '3',
    type: FeedbackType.FEATURE_REQUEST,
    title: 'Export data to CSV',
    description: 'Add functionality to export table data to CSV format for reporting purposes.',
    metadata: { 
      tables: ['users', 'jobs', 'industries'],
      priority: 'low'
    },
    status: 'completed',
    submittedBy: '<EMAIL>',
    createdAt: new Date('2024-01-10T14:20:00Z'),
    updatedAt: new Date('2024-01-18T11:15:00Z')
  },
  {
    id: '4',
    type: FeedbackType.COMPLAINT,
    title: 'Slow page loading times',
    description: 'The dashboard takes too long to load, especially on mobile devices.',
    metadata: { 
      loadTime: '8.5 seconds',
      device: 'iPhone 12',
      priority: 'high'
    },
    status: 'open',
    submittedBy: '<EMAIL>',
    createdAt: new Date('2024-01-12T11:10:00Z'),
    updatedAt: new Date('2024-01-12T11:10:00Z')
  },
  {
    id: '5',
    type: FeedbackType.COMPLIMENT,
    title: 'Great user interface',
    description: 'The new design is very intuitive and easy to use. Great job!',
    metadata: { 
      rating: 5,
      priority: 'low'
    },
    status: 'acknowledged',
    submittedBy: '<EMAIL>',
    createdAt: new Date('2024-01-20T08:30:00Z'),
    updatedAt: new Date('2024-01-20T08:30:00Z')
  }
])

// Dialog states
const showFeedbackDialog = ref(false)
const isEditMode = ref(false)
const selectedFeedback = ref(null)

// Form data
const feedback = reactive({
  type: FeedbackType.GENERAL,
  title: '',
  description: '',
  metadata: {}
})

// Search and filters
const searchTerm = ref('')
const selectedTypeFilter = ref([])
const selectedStatusFilter = ref([])
const dateRange = ref([])

// Options for dropdowns
const feedbackTypeOptions = Object.values(FeedbackType).map(type => ({
  label: type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  value: type
}))

const statusOptions = [
  { label: 'Open', value: 'open' },
  { label: 'In Progress', value: 'in_progress' },
  { label: 'Completed', value: 'completed' },
  { label: 'Acknowledged', value: 'acknowledged' },
  { label: 'Rejected', value: 'rejected' }
]

// Computed properties
const filteredFeedbacks = computed(() => {
  let filtered = feedbacks.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(feedback => 
      feedback.title.toLowerCase().includes(search) ||
      feedback.description.toLowerCase().includes(search) ||
      feedback.submittedBy.toLowerCase().includes(search)
    )
  }

  if (selectedTypeFilter.value.length > 0) {
    filtered = filtered.filter(feedback => 
      selectedTypeFilter.value.includes(feedback.type)
    )
  }

  if (selectedStatusFilter.value.length > 0) {
    filtered = filtered.filter(feedback => 
      selectedStatusFilter.value.includes(feedback.status)
    )
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(feedback => {
      const feedbackDate = new Date(feedback.createdAt)
      return feedbackDate >= startDate && feedbackDate <= endDate
    })
  }

  return filtered
})

const feedbackStats = computed(() => {
  const total = feedbacks.value.length
  const open = feedbacks.value.filter(f => f.status === 'open').length
  const inProgress = feedbacks.value.filter(f => f.status === 'in_progress').length
  const completed = feedbacks.value.filter(f => f.status === 'completed').length
  const byType = feedbacks.value.reduce((acc, feedback) => {
    acc[feedback.type] = (acc[feedback.type] || 0) + 1
    return acc
  }, {})
  
  return {
    total,
    open,
    inProgress,
    completed,
    completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
    byType
  }
})

// Methods
const getFeedbackTypeColor = (type) => {
  const colors = {
    [FeedbackType.SUGGESTION]: 'info',
    [FeedbackType.BUG_REPORT]: 'danger',
    [FeedbackType.FEATURE_REQUEST]: 'success',
    [FeedbackType.COMPLAINT]: 'warning',
    [FeedbackType.COMPLIMENT]: 'success',
    [FeedbackType.GENERAL]: 'secondary'
  }
  return colors[type] || 'secondary'
}

const getStatusColor = (status) => {
  const colors = {
    open: 'info',
    in_progress: 'warning',
    completed: 'success',
    acknowledged: 'secondary',
    rejected: 'danger'
  }
  return colors[status] || 'secondary'
}

const openFeedbackDialog = (feedback = null) => {
  isEditMode.value = !!feedback
  if (feedback) {
    feedbackForm.type = feedback.type
    feedbackForm.title = feedback.title
    feedbackForm.description = feedback.description
    feedbackForm.metadata = feedback.metadata || {}
    selectedFeedback.value = feedback
  } else {
    resetFeedbackForm()
  }
  showFeedbackDialog.value = true
}

const resetFeedbackForm = () => {
  feedbackForm.type = FeedbackType.GENERAL
  feedbackForm.title = ''
  feedbackForm.description = ''
  feedbackForm.metadata = {}
  selectedFeedback.value = null
}

const saveFeedback = () => {
  if (!feedbackForm.title.trim() || !feedbackForm.description.trim()) return

  if (isEditMode.value && selectedFeedback.value) {
    // Update existing feedback
    const index = feedbacks.value.findIndex(f => f.id === selectedFeedback.value.id)
    if (index !== -1) {
      feedbacks.value[index] = {
        ...feedbacks.value[index],
        type: feedbackForm.type,
        title: feedbackForm.title.trim(),
        description: feedbackForm.description.trim(),
        metadata: feedbackForm.metadata,
        updatedAt: new Date()
      }
    }
  } else {
    // Add new feedback
    const newFeedback = {
      id: Date.now().toString(),
      type: feedbackForm.type,
      title: feedbackForm.title.trim(),
      description: feedbackForm.description.trim(),
      metadata: feedbackForm.metadata,
      status: 'open',
      submittedBy: '<EMAIL>', // In real app, get from auth
      createdAt: new Date(),
      updatedAt: new Date()
    }
    feedbacks.value.push(newFeedback)
  }

  showFeedbackDialog.value = false
  resetFeedbackForm()
}

const deleteFeedback = (feedback) => {
  confirm.require({
    message: `Are you sure you want to delete the feedback "${feedback.title}"?`,
    header: 'Delete Feedback',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Cancel',
    acceptLabel: 'Delete',
    accept: () => {
      const index = feedbacks.value.findIndex(f => f.id === feedback.id)
      if (index !== -1) {
        feedbacks.value.splice(index, 1)
      }
    }
  })
}

const updateFeedbackStatus = (feedback, newStatus) => {
  const index = feedbacks.value.findIndex(f => f.id === feedback.id)
  if (index !== -1) {
    feedbacks.value[index].status = newStatus
    feedbacks.value[index].updatedAt = new Date()
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const clearFilters = () => {
  searchTerm.value = ''
  selectedTypeFilter.value = []
  selectedStatusFilter.value = []
  dateRange.value = []
}

const exportFeedbacks = () => {
  // Simple CSV export
  const csvContent = [
    ['ID', 'Type', 'Title', 'Description', 'Status', 'Submitted By', 'Created At'].join(','),
    ...filteredFeedbacks.value.map(feedback => [
      feedback.id,
      feedback.type,
      `"${feedback.title.replace(/"/g, '""')}"`,
      `"${feedback.description.replace(/"/g, '""')}"`,
      feedback.status,
      feedback.submittedBy,
      feedback.createdAt.toISOString()
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `feedback-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}
</script>

<template>
  <div class="feedback-view">
    <!-- Left Sidebar with Filters -->
    <div class="filters-sidebar">
      <div class="filters-header">
        <h3>
          <i class="pi pi-filter"></i>
          Filters
        </h3>
        <Button 
          icon="pi pi-filter-slash" 
          text
          rounded
          size="small"
          @click="clearFilters"
          :disabled="!searchTerm && selectedTypeFilter.length === 0 && selectedStatusFilter.length === 0"
          v-tooltip="'Clear All Filters'"
        />
      </div>

      <div class="filters-content">
        <!-- Search -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-search"></i>
            Search
          </label>
          <InputText 
            v-model="searchTerm" 
            placeholder="Search feedback..." 
            class="w-full"
          />
        </div>

        <!-- Type Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-comments"></i>
            Type
          </label>
          <MultiSelect
            v-model="selectedTypeFilter"
            :options="feedbackTypeOptions"
            option-label="label"
            option-value="value"
            placeholder="Select types"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Status Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-check-circle"></i>
            Status
          </label>
          <MultiSelect
            v-model="selectedStatusFilter"
            :options="statusOptions"
            option-label="label"
            option-value="value"
            placeholder="Select status"
            class="w-full"
            :max-selected-labels="2"
          />
        </div>

        <!-- Date Range Filter -->
        <div class="filter-group">
          <label class="filter-label">
            <i class="pi pi-calendar"></i>
            Date Range
          </label>
          <Calendar
            v-model="dateRange"
            selection-mode="range"
            :manual-input="false"
            placeholder="Select date range"
            class="w-full"
          />
        </div>

        <!-- Statistics -->
        <div class="filter-stats">
          <h4>Statistics</h4>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">Total Feedback</span>
              <span class="stat-value">{{ feedbackStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Open</span>
              <span class="stat-value info">{{ feedbackStats.open }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">In Progress</span>
              <span class="stat-value warning">{{ feedbackStats.inProgress }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Completed</span>
              <span class="stat-value success">{{ feedbackStats.completed }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Bug Reports</span>
              <span class="stat-value">{{ feedbackStats.byType.BUG_REPORT || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div>
            <h1>Feedback Management</h1>
            <p>Manage user feedback, suggestions, and bug reports</p>
          </div>
          <div class="header-actions">
            <Button 
              label="Export" 
              icon="pi pi-download" 
              severity="secondary"
              @click="exportFeedbacks"
            />
            <Button 
              label="Add Feedback" 
              icon="pi pi-plus" 
              @click="openFeedbackDialog()"
            />
          </div>
        </div>
      </div>

      <!-- Feedback Table -->
      <div class="table-container">
        <DataTable 
          :value="filteredFeedbacks" 
          class="feedback-table"
          :paginator="true" 
          :rows="20"
          :rows-per-page-options="[10, 20, 50]"
          stripedRows
          :rowHover="true"
          sort-field="createdAt"
          :sort-order="-1"
          :scrollable="true"
          scroll-height="flex"
        >
          <Column field="type" header="Type" sortable style="width: 140px">
            <template #body="slotProps">
              <Tag 
                :value="slotProps.data.type.replace(/_/g, ' ')" 
                :severity="getFeedbackTypeColor(slotProps.data.type)"
              />
            </template>
          </Column>
          
          <Column field="title" header="Title" sortable>
            <template #body="slotProps">
              <div class="feedback-title-cell">
                <span class="feedback-title" :title="slotProps.data.title">
                  {{ slotProps.data.title }}
                </span>
                <span class="feedback-submitter">by {{ slotProps.data.submittedBy }}</span>
              </div>
            </template>
          </Column>
          
          <Column field="description" header="Description" sortable>
            <template #body="slotProps">
              <span class="description-text" :title="slotProps.data.description">
                {{ slotProps.data.description }}
              </span>
            </template>
          </Column>
          
          <Column field="status" header="Status" sortable style="width: 120px">
            <template #body="slotProps">
              <Dropdown
                :model-value="slotProps.data.status"
                :options="statusOptions"
                option-label="label"
                option-value="value"
                @change="updateFeedbackStatus(slotProps.data, $event.value)"
                class="status-dropdown"
              />
            </template>
          </Column>
          
          <Column field="createdAt" header="Created" sortable style="width: 150px">
            <template #body="slotProps">
              <span class="created-date">{{ formatDate(slotProps.data.createdAt) }}</span>
            </template>
          </Column>
          
          <Column header="Actions" style="width: 120px">
            <template #body="slotProps">
              <div class="action-buttons">
                <Button 
                  icon="pi pi-eye" 
                  text 
                  rounded 
                  size="small" 
                  @click="openFeedbackDialog(slotProps.data)"
                  v-tooltip="'View/Edit'"
                />
                <Button 
                  icon="pi pi-trash" 
                  text 
                  rounded 
                  size="small" 
                  severity="danger"
                  @click="deleteFeedback(slotProps.data)"
                  v-tooltip="'Delete'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Feedback Dialog -->
    <Dialog 
      v-model:visible="showFeedbackDialog" 
      :header="isEditMode ? 'Edit Feedback' : 'Add Feedback'"
      :modal="true" 
      class="feedback-dialog"
      :style="{ width: '700px' }"
    >
      <div class="dialog-content">
        <div class="form-grid">
          <div class="form-row">
            <div class="form-group">
              <label for="feedback-type">Type *</label>
              <Dropdown
                id="feedback-type"
                v-model="feedbackForm.type"
                :options="feedbackTypeOptions"
                option-label="label"
                option-value="value"
                class="w-full"
              />
            </div>
          </div>
          
          <div class="form-group">
            <label for="feedback-title">Title *</label>
            <InputText 
              id="feedback-title"
              v-model="feedbackForm.title" 
              placeholder="Enter feedback title"
              class="w-full"
              :class="{ 'p-invalid': !feedbackForm.title.trim() }"
            />
          </div>
          
          <div class="form-group">
            <label for="feedback-description">Description *</label>
            <Textarea 
              id="feedback-description"
              v-model="feedbackForm.description" 
              placeholder="Enter detailed description"
              rows="6"
              class="w-full"
              :class="{ 'p-invalid': !feedbackForm.description.trim() }"
            />
          </div>
          
          <div class="form-group" v-if="isEditMode && selectedFeedback?.metadata">
            <label>Metadata</label>
            <pre class="metadata-display">{{ JSON.stringify(selectedFeedback.metadata, null, 2) }}</pre>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <Button 
            label="Cancel" 
            severity="secondary" 
            @click="showFeedbackDialog = false"
          />
          <Button 
            :label="isEditMode ? 'Update' : 'Create'" 
            @click="saveFeedback"
            :disabled="!feedbackForm.title.trim() || !feedbackForm.description.trim()"
          />
        </div>
      </template>
    </Dialog>

    <ConfirmDialog />
  </div>
</template>

<style scoped>
.feedback-view {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Filters Sidebar */
.filters-sidebar {
  width: 320px;
  background: var(--p-surface-card);
  border-right: 1px solid var(--p-surface-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filters-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--p-surface-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--p-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label i {
  font-size: 0.75rem;
  color: var(--p-text-muted-color);
}

.filter-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

.filter-stats h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--p-surface-50);
  border-radius: 6px;
}

:global(.dark) .stat-item {
  background: var(--p-surface-800);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--p-text-muted-color);
}

.stat-value {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.stat-value.info {
  color: var(--p-blue-600);
}

.stat-value.warning {
  color: var(--p-yellow-600);
}

.stat-value.success {
  color: var(--p-green-600);
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--p-surface-border);
  background: var(--p-surface-card);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--p-text-color);
}

.header-content p {
  margin: 0;
  color: var(--p-text-muted-color);
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* Table Container */
.table-container {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.feedback-table {
  flex: 1;
  height: 100%;
}

.feedback-title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.feedback-title {
  color: var(--p-text-color);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.feedback-submitter {
  color: var(--p-text-muted-color);
  font-size: 0.75rem;
}

.description-text {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.status-dropdown {
  min-width: 120px;
}

.created-date {
  color: var(--p-text-muted-color);
  font-size: 0.875rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.dialog-content {
  padding: 1rem 0;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--p-text-color);
  font-size: 0.875rem;
}

.metadata-display {
  background: var(--p-surface-50);
  border: 1px solid var(--p-surface-border);
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--p-text-color);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
}

:global(.dark) .metadata-display {
  background: var(--p-surface-800);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

@media (max-width: 1200px) {
  .filters-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .feedback-view {
    flex-direction: column;
    height: auto;
  }
  
  .filters-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
  }
  
  .main-content {
    order: 1;
    height: 60vh;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .table-container {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Custom scrollbar for filters */
.filters-content::-webkit-scrollbar {
  width: 6px;
}

.filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-100);
}

.filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-300);
  border-radius: 3px;
}

.filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-400);
}

:global(.dark) .filters-content::-webkit-scrollbar-track {
  background: var(--p-surface-800);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb {
  background: var(--p-surface-600);
}

:global(.dark) .filters-content::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-500);
}
</style>