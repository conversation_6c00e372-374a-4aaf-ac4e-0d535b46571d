import { Injectable, Logger, UnauthorizedException, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { UserEntity } from '../../users/entities/user.entity';
import { OtpEntity } from '../entities/otp.entity';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { UserRole, SubscriptionType } from '../../users/enums/user.enum';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EmailService } from '../../email/email.service';
import { ProfileEntity } from '../../users/entities/profile.entity';
import { CompanyEntity } from '../../companies/entities/company.entity';
import validator from 'validator';
interface RateLimitAttempt {
  count: number;
  timestamp: number;
}

@Injectable()
export class OtpService implements OnModuleInit {
  private readonly logger = new Logger(OtpService.name);
  private transporter: nodemailer.Transporter;
  private readonly OTP_EXPIRY_MINUTES = 10;
  private readonly MAX_OTP_ATTEMPTS = 3;
  private readonly OTP_COOLDOWN_MINUTES = 5;
  private readonly rateLimits: Map<string, RateLimitAttempt> = new Map();
  private readonly RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour
  private readonly MAX_ATTEMPTS = 5;

  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(OtpEntity)
    private readonly otpRepository: Repository<OtpEntity>,
    @InjectRepository(ProfileEntity)
    private readonly profileRepository: Repository<ProfileEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepository: Repository<CompanyEntity>,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
  ) {
    this.initializeTransporter();
  }

  onModuleInit() {
    // Run cleanup immediately on startup
    this.cleanupExpiredOTPs();
    // Start rate limit cleanup job
    this.startRateLimitCleanup();
  }

  @Cron(CronExpression.EVERY_HOUR)
  async cleanupExpiredOTPs() {
    try {
      const expiryDate = new Date();
      expiryDate.setMinutes(expiryDate.getMinutes() - this.OTP_EXPIRY_MINUTES);

      const result = await this.otpRepository.delete({
        expiresAt: LessThan(expiryDate),
      });

      this.logger.log(`Cleaned up ${result.affected} expired OTPs`);
    } catch (error) {
      this.logger.error('Failed to cleanup expired OTPs:', error);
    }
  }

  private startRateLimitCleanup() {
    setInterval(() => {
      const now = new Date();
      for (const [email, attempt] of this.rateLimits.entries()) {
        const timeSinceWindowStart = (now.getTime() - attempt.timestamp) / (1000 * 60);
        if (timeSinceWindowStart >= this.OTP_COOLDOWN_MINUTES) {
          this.rateLimits.delete(email);
          this.logger.debug(`Cleared rate limit for ${email}`);
        }
      }
    }, 60000); // Run every minute
  }

  private checkRateLimit(email: string): void {
    const now = Date.now();
    const userLimit = this.rateLimits.get(email);

    if (userLimit) {
      if (now - userLimit.timestamp < this.RATE_LIMIT_WINDOW) {
        if (userLimit.count >= this.MAX_ATTEMPTS) {
          throw new UnauthorizedException('Rate limit exceeded. Please try again later.');
        }
        userLimit.count++;
      } else {
        this.rateLimits.set(email, { count: 1, timestamp: now });
      }
    } else {
      this.rateLimits.set(email, { count: 1, timestamp: now });
    }
  }

  private initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransport({
        host: this.configService.get('SMTP_HOST'),
        port: this.configService.get('SMTP_PORT'),
        secure: this.configService.get('SMTP_SECURE') === 'true',
        auth: {
          user: this.configService.get('SMTP_USER'),
          pass: this.configService.get('SMTP_PASS'),
        },
      });

      // Verify transporter configuration
      this.transporter.verify((error) => {
        if (error) {
          this.logger.error('SMTP configuration error:', error);
        } else {
          this.logger.log('SMTP server is ready to send emails');
        }
      });
    } catch (error) {
      this.logger.error('Failed to initialize email transporter:', error);
      throw error;
    }
  }

  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private generateEmailTemplate(otp: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Your Login OTP</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .container {
              background-color: #f9f9f9;
              border-radius: 5px;
              padding: 20px;
              margin: 20px 0;
            }
            .otp-code {
              font-size: 32px;
              font-weight: bold;
              color: #007bff;
              text-align: center;
              padding: 20px;
              background-color: #e9ecef;
              border-radius: 5px;
              margin: 20px 0;
            }
            .footer {
              font-size: 12px;
              color: #666;
              text-align: center;
              margin-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>Your Login OTP</h2>
            <p>Please use the following OTP to complete your login:</p>
            <div class="otp-code">${otp}</div>
            <p>This OTP will expire in ${this.OTP_EXPIRY_MINUTES} minutes.</p>
            <p>If you didn't request this OTP, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </body>
      </html>
    `;
  }

  async sendOTP(email: string, userType: string): Promise<void> {
    try {
      // check for valid email - Additional check already being handled in email-otp-login 3top
      if (!validator.isEmail(email)) {
        throw new UnauthorizedException('Invalid Email');
      }

      const reqRole = UserRole[userType];

      if (!reqRole) {
        throw new UnauthorizedException('Invalid Application');
      }
      // Check rate limit
      this.checkRateLimit(email);

      // Check if user exists and is admin/superadmin
      let user = await this.userRepository.findOne({ where: { email } });
      if (user && (user.role === UserRole.ADMIN || user.role === UserRole.SUPER_ADMIN)) {
        throw new UnauthorizedException('Admin users must use password login.');
      }

      // Create new user if doesn't exist
      if (!user) {
        this.logger.log(`Creating new user for email: ${email}`);
        // Generate a random password for OTP users
        const randomPassword = Math.random().toString(36).slice(-8);
        user = this.userRepository.create({
          email,
          password: randomPassword, // Add default password
          role: reqRole,
          subscriptionType: SubscriptionType.DEFAULT,
          isEmailVerified: true,
          isPhoneVerified: false,
          isProfileComplete: false,
          isAadharVerified: false,
          isBlocked: false,
        });
        await this.userRepository.save(user);

        // Create profile for new user
        const profile = this.profileRepository.create({
          userId: user.id,
          isComplete: false,
        });
        const savedProfile = await this.profileRepository.save(profile);

        // Update user with profileId
        user.profileId = savedProfile.id;
        await this.userRepository.save(user);

        // If user is an employer, create a default company record
        if (reqRole === UserRole.EMPLOYER) {
          this.logger.log(`Creating default company for employer: ${email}`);
          const company = this.companyRepository.create({
            userId: user.id,
            name: `${user.email.split('@')[0]}'s Company`, // Default company name from email
            isActive: true,
          });
          await this.companyRepository.save(company);
        }
      }

      if (user && user.role !== reqRole) {
        throw new UnauthorizedException('You are not allowed to access this role');
      }

      // Generate OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Update user with OTP
      user.emailOtp = otp;
      user.emailOtpExpiry = otpExpiry;
      await this.userRepository.save(user);

      // Send OTP email
      await this.emailService.sendOtpEmail(email, otp);
    } catch (error) {
      this.logger.error(`Error sending OTP to ${email}: ${error.message}`);
      throw error;
    }
  }

  async verifyOTP(email: string, otp: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['profile', 'company']
    });

    console.log(user)
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (!user.emailOtp || !user.emailOtpExpiry) {
      throw new UnauthorizedException('No OTP requested');
    }

    if (user.emailOtp !== otp) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (new Date() > user.emailOtpExpiry) {
      throw new UnauthorizedException('OTP expired');
    }

    // Clear OTP after successful verification
    user.emailOtp = null;
    user.emailOtpExpiry = null;
    await this.userRepository.save(user);

    return true;
  }

  async findOrCreateUser(email: string): Promise<UserEntity> {
    let user = await this.userRepository.findOne({
      where: { email },
      relations: ['profile'],
    });

    if (!user) {
      const randomPassword = Math.random().toString(36).slice(-8);
      user = this.userRepository.create({
        email,
        password: randomPassword,
        role: UserRole.JOB_SEEKER,
        subscriptionType: SubscriptionType.DEFAULT,
        isEmailVerified: true,
        isPhoneVerified: false,
        isProfileComplete: false,
        isAadharVerified: false,
        isBlocked: false,
      });
      await this.userRepository.save(user);

      // Create profile for new user
      const profile = this.profileRepository.create({
        userId: user.id,
        isComplete: false,
      });
      const savedProfile = await this.profileRepository.save(profile);

      // Update user with profileId
      user.profileId = savedProfile.id;
      await this.userRepository.save(user);
    }

    return user;
  }
}
