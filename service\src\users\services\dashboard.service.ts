import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual } from 'typeorm';
import { UserEntity } from '../entities/user.entity';
import { AdminActivityEntity } from '../entities/admin-activity.entity';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(AdminActivityEntity)
    private readonly adminActivityRepository: Repository<AdminActivityEntity>,
  ) {}

  async getUserGrowth(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [dailyRegistrations, totalUsers] = await Promise.all([
      this.userRepository
        .createQueryBuilder('user')
        .select('DATE(user.createdAt)', 'date')
        .addSelect('COUNT(*)', 'count')
        .where('user.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .groupBy('DATE(user.createdAt)')
        .orderBy('date', 'ASC')
        .getRawMany(),

      this.userRepository.count(),
    ]);

    const growthRate =
      dailyRegistrations.length > 0
        ? (dailyRegistrations[dailyRegistrations.length - 1].count / totalUsers) * 100
        : 0;

    return {
      period: {
        start: startDate,
        end: endDate,
      },
      dailyRegistrations,
      totalUsers,
      growthRate: growthRate.toFixed(2),
    };
  }

  async getSubscriptionStats() {
    const [totalUsers, subscriptionDistribution] = await Promise.all([
      this.userRepository.count(),
      this.userRepository
        .createQueryBuilder('user')
        .select('user.subscriptionType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('user.subscriptionType')
        .getRawMany(),
    ]);

    return {
      totalUsers,
      subscriptionDistribution,
    };
  }

  async getRoleDistribution() {
    const [totalUsers, roleDistribution] = await Promise.all([
      this.userRepository.count(),
      this.userRepository
        .createQueryBuilder('user')
        .select('user.role', 'role')
        .addSelect('COUNT(*)', 'count')
        .groupBy('user.role')
        .getRawMany(),
    ]);

    return {
      totalUsers,
      roleDistribution,
    };
  }

  async getAdminActivity(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [activityCounts, actionTypes] = await Promise.all([
      this.adminActivityRepository
        .createQueryBuilder('activity')
        .select('DATE(activity.createdAt)', 'date')
        .addSelect('COUNT(*)', 'count')
        .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .groupBy('DATE(activity.createdAt)')
        .orderBy('date', 'ASC')
        .getRawMany(),

      this.adminActivityRepository
        .createQueryBuilder('activity')
        .select('activity.actionType', 'type')
        .addSelect('COUNT(*)', 'count')
        .where('activity.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .groupBy('activity.actionType')
        .getRawMany(),
    ]);

    return {
      period: {
        start: startDate,
        end: endDate,
      },
      dailyActivity: activityCounts,
      actionTypes,
    };
  }

  async getDashboardOverview() {
    const [userGrowth, subscriptionStats, roleDistribution, adminActivity] = await Promise.all([
      this.getUserGrowth(),
      this.getSubscriptionStats(),
      this.getRoleDistribution(),
      this.getAdminActivity(),
    ]);

    return {
      userGrowth,
      subscriptionStats,
      roleDistribution,
      adminActivity,
    };
  }

  async getUserEngagementMetrics(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [activeUsers, verifiedUsers, profileCompletion] = await Promise.all([
      this.userRepository.count({
        where: {
          updatedAt: Between(startDate, endDate),
        },
      }),
      this.userRepository.count({
        where: {
          isEmailVerified: true,
          isPhoneVerified: true,
        },
      }),
      this.userRepository.count({
        where: {
          isProfileComplete: true,
        },
      }),
    ]);

    const totalUsers = await this.userRepository.count();

    return {
      period: {
        start: startDate,
        end: endDate,
      },
      metrics: {
        activeUsers,
        activeUserRate: (activeUsers / totalUsers) * 100,
        verifiedUsers,
        verificationRate: (verifiedUsers / totalUsers) * 100,
        profileCompletion,
        profileCompletionRate: (profileCompletion / totalUsers) * 100,
      },
    };
  }

  async getRetentionRates(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get users who registered before the start date
    const cohortUsers = await this.userRepository.find({
      where: {
        createdAt: LessThanOrEqual(startDate),
      },
    });

    // Get users who were active in the period
    const activeUsers = await this.userRepository.find({
      where: {
        updatedAt: Between(startDate, endDate),
      },
    });

    const retentionRate = (activeUsers.length / cohortUsers.length) * 100;

    return {
      period: {
        start: startDate,
        end: endDate,
      },
      metrics: {
        cohortSize: cohortUsers.length,
        activeUsers: activeUsers.length,
        retentionRate,
      },
    };
  }

  async exportUserData(format: 'csv' | 'excel' = 'csv') {
    const users = await this.userRepository.find({
      relations: ['profile'],
    });

    const data = users.map((user) => ({
      id: user.id,
      email: user.email,
      phone: user.phone,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      subscriptionType: user.subscriptionType,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      isProfileComplete: user.isProfileComplete,
      isAadharVerified: user.isAadharVerified,
      isBlocked: user.isBlocked,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      address: user.profile?.address,
      city: user.profile?.city,
      state: user.profile?.state,
      country: user.profile?.country,
      postalCode: user.profile?.postalCode,
    }));

    if (format === 'excel') {
      // TODO: Implement Excel export using a library like exceljs
      return {
        format: 'excel',
        data,
        message: 'Excel export not implemented yet',
      };
    }

    // CSV export
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map((row) => Object.values(row).join(','));
    const csv = [headers, ...rows].join('\n');

    return {
      format: 'csv',
      data: csv,
      filename: `users_export_${new Date().toISOString()}.csv`,
    };
  }

  async exportAdminActivity(format: 'csv' | 'excel' = 'csv') {
    const activities = await this.adminActivityRepository.find({
      relations: ['admin'],
      order: { createdAt: 'DESC' },
    });

    const data = activities.map((activity) => ({
      id: activity.id,
      actionType: activity.actionType,
      targetId: activity.targetId,
      adminId: activity.admin?.id,
      adminEmail: activity.admin?.email,
      details: JSON.stringify(activity.details),
      createdAt: activity.createdAt,
    }));

    if (format === 'excel') {
      // TODO: Implement Excel export using a library like exceljs
      return {
        format: 'excel',
        data,
        message: 'Excel export not implemented yet',
      };
    }

    // CSV export
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map((row) => Object.values(row).join(','));
    const csv = [headers, ...rows].join('\n');

    return {
      format: 'csv',
      data: csv,
      filename: `admin_activity_export_${new Date().toISOString()}.csv`,
    };
  }

  async getRecentActivities(limit: number = 10) {
    const activities = await this.adminActivityRepository.find({
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['admin'],
    });

    return activities.map((activity) => ({
      id: activity.id,
      actionType: activity.actionType,
      targetId: activity.targetId,
      adminId: activity.admin?.id,
      adminEmail: activity.admin?.email,
      details: activity.details,
      createdAt: activity.createdAt,
    }));
  }
}
